import { databases, APPWRITE_CONFIG } from ".";
import { ProjectMilestone } from "@/lib/types";
import { Query, ID, AppwriteException } from "appwrite";
import { withRetry, normalizeDate } from "@/lib/utils";

// Helper function to map Appwrite document to ProjectMilestone
const mapToMilestone = (doc: any): ProjectMilestone => ({
  id: doc.$id,
  projectId: doc.projectId,
  title: doc.title,
  description: doc.description,
  dueDate: doc.dueDate,
  completed: doc.completed,
  completedAt: doc.completedAt
});

// Use the normalizeDate function from utils instead of redefining it

export async function getMilestonesByProjectId(projectId: string): Promise<ProjectMilestone[]> {
  return withRetry(async () => {
    try {
      const response = await databases.listDocuments(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.milestones,
        [
          Query.equal("projectId", projectId),
          Query.orderAsc("dueDate")
        ]
      );
      return response.documents.map(mapToMilestone);
    } catch (error) {
      if (error instanceof AppwriteException && error.code === 401) {
        console.error("Authentication error fetching milestones:", error.message);
        // Return empty array instead of throwing to prevent UI errors
        return [];
      }
      throw error;
    }
  });
}

export async function createMilestone(milestone: Omit<ProjectMilestone, "id">): Promise<ProjectMilestone> {
  return withRetry(async () => {
    const doc = await databases.createDocument(
      APPWRITE_CONFIG.databaseId,
      APPWRITE_CONFIG.collections.milestones,
      ID.unique(),
      {
        projectId: milestone.projectId,
        title: milestone.title,
        description: milestone.description || "",
        dueDate: normalizeDate(milestone.dueDate),
        completed: milestone.completed,
        completedAt: normalizeDate(milestone.completedAt)
      }
    );
    return mapToMilestone(doc);
  });
}

export async function updateMilestone(
  milestoneId: string,
  updates: Partial<ProjectMilestone>
): Promise<ProjectMilestone> {
  return withRetry(async () => {
    // Only include fields that are actually being updated
    const updateData: Record<string, any> = {};
    if (updates.title !== undefined) updateData.title = updates.title;
    if (updates.description !== undefined) updateData.description = updates.description;
    if (updates.dueDate !== undefined) updateData.dueDate = normalizeDate(updates.dueDate);
    if (updates.completed !== undefined) updateData.completed = updates.completed;
    if (updates.completedAt !== undefined) updateData.completedAt = normalizeDate(updates.completedAt);

    const doc = await databases.updateDocument(
      APPWRITE_CONFIG.databaseId,
      APPWRITE_CONFIG.collections.milestones,
      milestoneId,
      updateData
    );
    return mapToMilestone(doc);
  });
}

export async function deleteMilestone(milestoneId: string): Promise<void> {
  return withRetry(async () => {
    await databases.deleteDocument(
      APPWRITE_CONFIG.databaseId,
      APPWRITE_CONFIG.collections.milestones,
      milestoneId
    );
  });
}

// Batch operations for better performance
export async function createMilestones(milestones: Array<Omit<ProjectMilestone, "id">>): Promise<ProjectMilestone[]> {
  if (!milestones.length) return [];
  
  // Process in batches of 10 for better performance
  const results: ProjectMilestone[] = [];
  const batchSize = 10;
  
  for (let i = 0; i < milestones.length; i += batchSize) {
    const batch = milestones.slice(i, i + batchSize);
    const batchResults = await Promise.all(batch.map(createMilestone));
    results.push(...batchResults);
  }
  
  return results;
}


