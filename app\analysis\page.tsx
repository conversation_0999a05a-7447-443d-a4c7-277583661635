"use client";

import { useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/layout/Header";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { StudentAnalyticsView } from "@/components/analytics/StudentAnalyticsView";
import { SupervisorAnalyticsView } from "@/components/analytics/SupervisorAnalyticsView";
import { ManagerAnalyticsView } from "@/components/analytics/ManagerAnalyticsView";
import {
  BarChart3,
  TrendingUp,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  FileText,
  Alert<PERSON>riangle,
  Info,
} from "lucide-react";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";

type TimeRange = "month" | "quarter" | "year";

export default function AnalysisPage() {
  const { user: currentUser } = useAuth();
  const router = useRouter();
  const [timeRange, setTimeRange] = useState<TimeRange>("quarter");

  // Check if user has permission to access analysis
  if (!currentUser) {
    return (
      <div className="min-h-screen bg-background flex flex-col">
        <Header />
        <div className="flex-1 flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Authentication Required</CardTitle>
              <CardDescription>
                Please log in to access the analysis dashboard.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => router.push("/auth/login")}>
                Go to Login
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Get role-specific configuration
  const getRoleConfig = () => {
    switch (currentUser.role) {
      case 'student':
        return {
          title: "My Academic Analytics",
          description: "Track your academic progress, project status, and performance metrics",
          icon: <BookOpen className="h-6 w-6" />,
          defaultTimeRange: "month" as TimeRange,
          availableTimeRanges: ["month", "quarter"] as TimeRange[],
        };
      case 'supervisor':
        return {
          title: "Supervision Analytics",
          description: "Monitor your supervised projects, student progress, and review workload",
          icon: <Users className="h-6 w-6" />,
          defaultTimeRange: "quarter" as TimeRange,
          availableTimeRanges: ["month", "quarter", "year"] as TimeRange[],
        };
      case 'manager':
      case 'admin':
        return {
          title: "Comprehensive Analytics Dashboard",
          description: "Complete organizational overview with detailed insights and reports",
          icon: <BarChart3 className="h-6 w-6" />,
          defaultTimeRange: "year" as TimeRange,
          availableTimeRanges: ["month", "quarter", "year"] as TimeRange[],
        };
      default:
        return {
          title: "Analytics Dashboard",
          description: "View your analytics and insights",
          icon: <TrendingUp className="h-6 w-6" />,
          defaultTimeRange: "month" as TimeRange,
          availableTimeRanges: ["month"] as TimeRange[],
        };
    }
  };

  const roleConfig = getRoleConfig();

  // Set default time range based on role if not already set
  if (timeRange !== roleConfig.defaultTimeRange && !roleConfig.availableTimeRanges.includes(timeRange)) {
    setTimeRange(roleConfig.defaultTimeRange);
  }

  const renderRoleSpecificAnalytics = () => {
    switch (currentUser.role) {
      case 'student':
        return <StudentAnalyticsView timeRange={timeRange} />;
      case 'supervisor':
        return <SupervisorAnalyticsView timeRange={timeRange} />;
      case 'manager':
      case 'admin':
        return <ManagerAnalyticsView timeRange={timeRange} />;
      default:
        return (
          <Card>
            <CardContent className="p-6">
              <div className="text-center text-muted-foreground">
                <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
                <p>Analytics not available for your role</p>
              </div>
            </CardContent>
          </Card>
        );
    }
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <Header />
      <div className="flex-1 container py-6 space-y-6">
        {/* Page Header */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              {roleConfig.icon}
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">{roleConfig.title}</h1>
              <p className="text-muted-foreground">{roleConfig.description}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Select
              value={timeRange}
              onValueChange={(value) => setTimeRange(value as TimeRange)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select time range" />
              </SelectTrigger>
              <SelectContent>
                {roleConfig.availableTimeRanges.map((range) => (
                  <SelectItem key={range} value={range}>
                    {range === "month" && "Last Month"}
                    {range === "quarter" && "Last Quarter"}
                    {range === "year" && "Last Year"}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Badge variant="secondary" className="text-sm">
              {currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1)}
            </Badge>
          </div>
        </div>

        {/* Role-specific Information Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Analytics Scope</AlertTitle>
          <AlertDescription>
            {currentUser.role === 'student' && 
              "You can view analytics for your own projects, documents, and academic progress."
            }
            {currentUser.role === 'supervisor' && 
              "You can view analytics for all projects you supervise and students under your guidance."
            }
            {(currentUser.role === 'manager' || currentUser.role === 'admin') && 
              "You have access to comprehensive analytics across all projects, users, and organizational metrics."
            }
          </AlertDescription>
        </Alert>

        {/* Analytics Content */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Overview
            </TabsTrigger>
            {(currentUser.role === 'manager' || currentUser.role === 'admin') && (
              <>
                <TabsTrigger value="detailed" className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Detailed Reports
                </TabsTrigger>
                <TabsTrigger value="trends" className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Trends & Insights
                </TabsTrigger>
              </>
            )}
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {renderRoleSpecificAnalytics()}
          </TabsContent>

          {(currentUser.role === 'manager' || currentUser.role === 'admin') && (
            <>
              <TabsContent value="detailed" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Detailed Reports</CardTitle>
                    <CardDescription>
                      Comprehensive reports and detailed analytics for management oversight
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold mb-2">Advanced Reports</h3>
                      <p className="text-muted-foreground mb-4">
                        Access detailed reports and export functionality
                      </p>
                      <Button onClick={() => router.push('/reports')}>
                        Go to Reports Page
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="trends" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Trends & Insights</CardTitle>
                    <CardDescription>
                      Long-term trends and predictive insights for strategic planning
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8">
                      <TrendingUp className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold mb-2">Advanced Analytics</h3>
                      <p className="text-muted-foreground mb-4">
                        Detailed trend analysis and predictive insights coming soon
                      </p>
                      <Button variant="outline" disabled>
                        Coming Soon
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </>
          )}
        </Tabs>
      </div>
    </div>
  );
}
