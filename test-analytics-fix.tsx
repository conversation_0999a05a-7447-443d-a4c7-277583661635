/**
 * Test component to verify the analytics hook fix
 */

import React from 'react';
import { useAnalytics } from '@/hooks/useAnalytics';

export function TestAnalyticsFix() {
  const {
    analytics,
    isLoading,
    error,
    refreshAnalytics
  } = useAnalytics({
    timeRange: 'month',
    enableRealTime: true,
    includeExtendedAnalytics: true
  });

  if (isLoading) {
    return <div>Loading analytics...</div>;
  }

  if (error) {
    return (
      <div>
        <p>Error: {error}</p>
        <button onClick={refreshAnalytics}>Retry</button>
      </div>
    );
  }

  if (!analytics) {
    return <div>No analytics data available</div>;
  }

  return (
    <div>
      <h2>Analytics Test</h2>
      <div>
        <p>Total Projects: {analytics.totalProjects}</p>
        <p>Total Users: {analytics.userEngagement.totalUsers}</p>
        <p>Total Documents: {analytics.documentAnalytics.totalDocuments}</p>
        <p>Total Milestones: {analytics.milestoneAnalytics.totalMilestones}</p>
      </div>
      <button onClick={refreshAnalytics}>Refresh Analytics</button>
    </div>
  );
}
