'use client';

import { Head<PERSON> } from '@/components/layout/Header';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { NotificationsPanel } from '@/components/notifications/NotificationsPanel';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { FileText, Users } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { databases, APPWRITE_CONFIG } from '@/lib/api';
import { Query } from 'appwrite';
import { useAuth } from '@/hooks/useAuth';
import { Document, Project, User } from '@/lib/types';
import { useToast } from '@/hooks/useToast';
import { useDocumentQueries } from '@/hooks/useDocument';
import { useUser } from '@/hooks/useUser';
import { useNotifications } from '@/hooks/useNotifications';
import { cache } from 'react';
import { useMemo } from 'react';

// Cache document filtering
const filterDocumentsByStatus = cache((documents: Document[], status: string) => {
  return documents.filter(doc => doc.status === status);
});

// Cache project filtering
const filterProjectsByStatus = cache((projects: Project[], status: string) => {
  return projects.filter(project => project.status === status);
});

// Cache finding student by ID
const findStudentById = cache((students: User[], studentId: string) => {
  return students.find(user => user.id === studentId);
});

export default function SupervisorDashboard() {
  const router = useRouter();
  const { user } = useAuth();
  const { toast } = useToast();
  const { useUsersByIdsQuery } = useUser();

  const { useSupervisorDocuments } = useDocumentQueries();
  const { data: assignedDocuments = [] } = useSupervisorDocuments(user?.id || '');

  // Fetch projects where this supervisor is assigned
  const { data: assignedProjects = [] } = useQuery<Project[]>({
    queryKey: ['supervisor-projects', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const response = await databases.listDocuments(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.projects,
        [
          Query.equal('supervisorIds', [user.id]),
          Query.orderDesc('$createdAt')
        ]
      );
      return response.documents.map(project => ({
        id: project.$id,
        title: project.title,
        description: project.description,
        studentId: project.studentId,
        supervisorIds: project.supervisorIds,
        status: project.status,
        documents: project.documents || [],
        deadline: new Date(project.deadline),
        createdAt: new Date(project.$createdAt),
        updatedAt: new Date(project.$updatedAt),
        lastActivity: new Date(project.$updatedAt),
        milestones: project.milestones || [],
        teamMembers: project.teamMembers || [],
        $id: project.$id
      }));
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000 // 5 minutes cache
  });

  // Fetch supervised students with memoized student IDs
  const studentIds = useMemo(() => {
    if (!assignedProjects?.length) return [];
    return Array.from(new Set(assignedProjects.map(p => p.studentId)));
  }, [assignedProjects]);

  const { data: supervisedStudents = [] } = useUsersByIdsQuery(studentIds);
  const { notifications, markAsRead } = useNotifications(user?.id || '');

  // Memoize filtered documents and projects
  const documentsUnderReview = useMemo(() =>
    filterDocumentsByStatus(assignedDocuments, 'under_review'),
    [assignedDocuments]
  );

  const activeProjects = useMemo(() =>
    filterProjectsByStatus(assignedProjects, 'active'),
    [assignedProjects]
  );

  // Navigation handler
  const handleNavigation = (path: string) => () => router.push(path);

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <Header />
      <main className="flex-1 flex justify-center">
        <div className="w-full max-w-7xl px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold tracking-tight">
                Welcome back, {user?.name || 'Supervisor'}
              </h1>
              <p className="text-sm md:text-base text-muted-foreground">
                Manage your supervised projects and review documents
              </p>
            </div>
            <NotificationsPanel
              notifications={notifications}
              onMarkAsRead={markAsRead}
            />
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Pending Reviews
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold mb-4">
                  {documentsUnderReview.length}
                </div>
                <ScrollArea className="h-[300px]">
                  <div className="space-y-4">
                    {documentsUnderReview.map((doc) => {
                      const student = findStudentById(supervisedStudents, doc.studentId);
                      return (
                        <div
                          key={doc.id}
                          className="block p-3 rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer"
                          onClick={handleNavigation(`/documents/${doc.id}`)}
                        >
                          <div className="flex items-start justify-between gap-2">
                            <div>
                              <p className="font-medium">{doc.title}</p>
                              <p className="text-sm text-muted-foreground">
                                By {student?.name || 'Unknown Student'}
                              </p>
                            </div>
                            <Badge variant="secondary" className="capitalize">
                              {doc.type}
                            </Badge>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Supervised Students
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold mb-4">
                  {supervisedStudents.length}
                </div>
                <ScrollArea className="h-[300px]">
                  <div className="space-y-4">
                    {supervisedStudents.map((student) => (
                      <div
                        key={student.id}
                        className="p-3 rounded-lg border"
                      >
                        <div className="flex items-center gap-3">
                          <Image
                            src={student.profileImage}
                            alt={student.name}
                            width={40}
                            height={40}
                            className="rounded-full"
                          />
                          <div>
                            <p className="font-medium">{student.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {student.department}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Active Projects</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold mb-4">
                  {activeProjects.length}
                </div>
                <ScrollArea className="h-[300px]">
                  <div className="space-y-4">
                    {activeProjects.map((project) => {
                      const student = findStudentById(supervisedStudents, project.studentId);
                      return (
                        <div
                          key={project.id}
                          className="p-3 rounded-lg border"
                        >
                          <p className="font-medium">{project.title}</p>
                          <p className="text-sm text-muted-foreground">
                            Student: {student?.name || 'Unknown Student'}
                          </p>
                          <div className="mt-2 flex justify-end">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleNavigation(`/projects/${project.id}`)}
                            >
                              View Details
                            </Button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          <div className="mt-6">
            <Tabs defaultValue="documents" className="space-y-4">
              <TabsList>
                <TabsTrigger value="documents">Documents</TabsTrigger>
                <TabsTrigger value="projects">Projects</TabsTrigger>
              </TabsList>

              <TabsContent value="documents">
                <Card>
                  <CardHeader>
                    <CardTitle>All Documents</CardTitle>
                    <CardDescription>
                      Review and manage student documents
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4">
                      {assignedDocuments.map((doc) => {
                        const student = findStudentById(supervisedStudents, doc.studentId);
                        return (
                          <div
                            key={doc.id}
                            className="flex items-center justify-between p-4 border rounded-lg"
                          >
                            <div>
                              <div className="flex items-center gap-2">
                                <h3 className="font-medium">{doc.title}</h3>
                                <Badge variant="secondary" className="capitalize">
                                  {doc.type}
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">
                                By {student?.name || 'Unknown Student'}
                              </p>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleNavigation(`/documents/${doc.id}`)}
                            >
                              Review
                            </Button>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="projects">
                <Card>
                  <CardHeader>
                    <CardTitle>All Projects</CardTitle>
                    <CardDescription>
                      View and manage your supervised projects
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4">
                      {assignedProjects.map((project) => {
                        const student = findStudentById(supervisedStudents, project.studentId);
                        return (
                          <div
                            key={project.id}
                            className="flex items-center justify-between p-4 border rounded-lg"
                          >
                            <div>
                              <h3 className="font-medium">{project.title}</h3>
                              <p className="text-sm text-muted-foreground">
                                Student: {student?.name || 'Unknown Student'}
                              </p>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleNavigation(`/projects/${project.id}`)}
                            >
                              View Details
                            </Button>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
    </div>
  );
}




