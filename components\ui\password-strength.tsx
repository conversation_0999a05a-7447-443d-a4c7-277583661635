'use client';

import { useEffect, useState } from 'react';

interface PasswordStrengthMeterProps {
  password: string;
}

export function PasswordStrengthMeter({ password }: PasswordStrengthMeterProps) {
  const [strength, setStrength] = useState(0);
  const [label, setLabel] = useState('');

  useEffect(() => {
    calculateStrength(password);
  }, [password]);

  const calculateStrength = (password: string) => {
    let score = 0;
    
    if (!password) {
      setStrength(0);
      setLabel('');
      return;
    }

    // Length check
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;

    // Complexity checks
    if (/[A-Z]/.test(password)) score += 1;
    if (/[a-z]/.test(password)) score += 1;
    if (/\d/.test(password)) score += 1;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;

    // Set final score (0-4)
    const finalScore = Math.min(4, Math.floor(score / 2));
    setStrength(finalScore);
    
    // Set label
    const labels = ['Weak', 'Fair', 'Good', 'Strong', 'Very Strong'];
    setLabel(labels[finalScore]);
  };

  const getColor = () => {
    const colors = ['bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-green-500', 'bg-emerald-500'];
    return colors[strength];
  };

  return (
    <div className="space-y-1">
      <div className="flex h-2 w-full overflow-hidden rounded-full bg-muted">
        <div 
          className={`${getColor()} transition-all duration-300`} 
          style={{ width: `${(strength + 1) * 20}%` }}
        />
      </div>
      {label && (
        <p className="text-xs text-muted-foreground text-right">{label}</p>
      )}
    </div>
  );
}