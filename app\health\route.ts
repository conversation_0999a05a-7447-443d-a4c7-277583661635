import { NextRequest, NextResponse } from 'next/server';

/**
 * Health check endpoint for deployment verification
 * Used by Jenkins pipeline and monitoring systems
 */
export async function GET(request: NextRequest) {
  try {
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      services: {
        database: 'connected', // You can add actual database health check here
        appwrite: 'connected',  // You can add actual Appwrite health check here
      },
      system: {
        memory: {
          used: process.memoryUsage().heapUsed,
          total: process.memoryUsage().heapTotal,
        },
        platform: process.platform,
        nodeVersion: process.version,
      }
    };

    return NextResponse.json(healthData, { status: 200 });
  } catch (error) {
    const errorData = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      environment: process.env.NODE_ENV || 'development',
    };

    return NextResponse.json(errorData, { status: 503 });
  }
}

/**
 * Simple ping endpoint for basic connectivity testing
 */
export async function HEAD(request: NextRequest) {
  return new NextResponse(null, { status: 200 });
}
