
import { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { User, UserRole } from '@/lib/types';
import { useToast } from '@/hooks/useToast';

interface UserDialogProps {
  user?: User | null;
  isNew?: boolean;
  onSave: (user: User) => Promise<void>;
  onClose?: () => void;
}

export function UserDialog({ user, isNew = false, onSave, onClose }: UserDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  
  const [formData, setFormData] = useState<User>({
    id: user?.id || '',
    email: user?.email || '',
    name: user?.name || '',
    role: user?.role || 'student',
    department: user?.department || '',
    specialization: user?.specialization || '',
    bio: user?.bio || '',
    profileImage: user?.profileImage || '',
  });

  useEffect(() => {
    if (user) {
      setFormData({
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        department: user.department || '',
        specialization: user.specialization || '',
        bio: user.bio || '',
        profileImage: user.profileImage || '',
      });
    }
  }, [user]);

  const handleChange = (field: keyof User, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when field is edited
    if (formErrors[field]) {
      setFormErrors(prev => {
        const updated = { ...prev };
        delete updated[field];
        return updated;
      });
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      errors.name = "Name is required";
    }
    
    if (!formData.email.trim()) {
      errors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Email is invalid";
    }
    
    if (!formData.role) {
      errors.role = "Role is required";
    }
    
    return errors;
  };

  const handleSave = useCallback(async () => {
    const errors = validateForm();
    
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    
    setLoading(true);
    try {
      await onSave(formData);
      onClose?.();
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: isNew 
          ? "Failed to create new user. Please try again."
          : "Failed to update user information. Please try again.",
      });
      console.error('User save error:', error);
    } finally {
      setLoading(false);
    }
  }, [formData, isNew, onClose, onSave, toast]);

  return (
    <DialogContent className="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>{isNew ? 'Add New User' : 'Edit User'}</DialogTitle>
      </DialogHeader>
      <div className="grid gap-4 py-4">
        <div className="grid gap-2">
          <Label htmlFor="name" className="required">Name</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => handleChange('name', e.target.value)}
            placeholder="Full Name"
            className={formErrors.name ? "border-destructive" : ""}
          />
          {formErrors.name && (
            <p className="text-sm text-destructive">{formErrors.name}</p>
          )}
        </div>
        
        <div className="grid gap-2">
          <Label htmlFor="email" className="required">Email</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleChange('email', e.target.value)}
            placeholder="Email Address"
            className={formErrors.email ? "border-destructive" : ""}
            disabled={!isNew} // Email can't be changed for existing users
          />
          {formErrors.email && (
            <p className="text-sm text-destructive">{formErrors.email}</p>
          )}
        </div>
        
        <div className="grid gap-2">
          <Label htmlFor="role" className="required">Role</Label>
          <Select 
            value={formData.role} 
            onValueChange={(value) => handleChange('role', value as UserRole)}
          >
            <SelectTrigger id="role" className={formErrors.role ? "border-destructive" : ""}>
              <SelectValue placeholder="Select role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="student">Student</SelectItem>
              <SelectItem value="supervisor">Supervisor</SelectItem>
              <SelectItem value="manager">Manager</SelectItem>
              <SelectItem value="admin">Admin</SelectItem>
            </SelectContent>
          </Select>
          {formErrors.role && (
            <p className="text-sm text-destructive">{formErrors.role}</p>
          )}
        </div>
        
        <div className="grid gap-2">
          <Label htmlFor="department">Department</Label>
          <Input
            id="department"
            value={formData.department}
            onChange={(e) => handleChange('department', e.target.value)}
            placeholder="Department"
          />
        </div>
        
        <div className="grid gap-2">
          <Label htmlFor="specialization">Specialization</Label>
          <Input
            id="specialization"
            value={formData.specialization}
            onChange={(e) => handleChange('specialization', e.target.value)}
            placeholder="Specialization"
          />
        </div>
        
        <div className="grid gap-2">
          <Label htmlFor="bio">Bio</Label>
          <Textarea
            id="bio"
            value={formData.bio}
            onChange={(e) => handleChange('bio', e.target.value)}
            placeholder="Short biography"
            rows={3}
          />
        </div>
      </div>
      
      {isNew && (
        <p className="text-sm text-muted-foreground mb-4">
          A temporary password will be generated for this user.
        </p>
      )}
      
      <DialogFooter>
        <Button variant="outline" onClick={onClose}>Cancel</Button>
        <Button onClick={handleSave} disabled={loading}>
          {loading ? 'Saving...' : isNew ? 'Create User' : 'Save Changes'}
        </Button>
      </DialogFooter>
    </DialogContent>
  );
}



