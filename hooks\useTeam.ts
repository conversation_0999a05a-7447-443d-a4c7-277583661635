import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { databases, APPWRITE_CONFIG } from '@/lib/api';
import { Query, AppwriteException } from 'appwrite';
import { User } from '@/lib/types';

interface TeamQueryOptions {
  staleTime?: number;
  gcTime?: number;
  retry?: number;
  enabled?: boolean;
}

export class TeamApiError extends Error {
  constructor(message: string, public originalError: unknown) {
    super(message);
    this.name = 'TeamApiError';
  }
}

export function useTeam() {
  const useTeamMembersQuery = (
    userId: string,
    options: TeamQueryOptions = {}
  ): UseQueryResult<User[], TeamApiError> => 
    useQuery({
      queryKey: ['teamMembers', userId],
      queryFn: async () => {
        if (!userId) {
          throw new TeamApiError('User ID is required', null);
        }
        
        try {
          // First query: Get all projects where user is a team member
          const projectsResponse = await databases.listDocuments(
            APPWRITE_CONFIG.databaseId,
            APPWRITE_CONFIG.collections.projects,
            [Query.equal('teamMembers', userId)]
          );
          
          // Extract and deduplicate member IDs
          const memberIds = Array.from(new Set(
            projectsResponse.documents.flatMap(project => 
              (project.teamMembers as string[]).filter(Boolean)
            )
          ));
          
          if (memberIds.length === 0) {
            return [];
          }

          // Second query: Get user profiles for team members
          const membersResponse = await databases.listDocuments(
            APPWRITE_CONFIG.databaseId,
            APPWRITE_CONFIG.collections.profiles,
            [Query.equal('$id', memberIds)]
          );
          
          return membersResponse.documents.map(doc => ({
            id: doc.$id,
            email: doc.email,
            name: doc.name,
            role: doc.role,
            department: doc.department,
            specialization: doc.specialization,
            bio: doc.bio,
            profileImage: doc.profileImage,
            status: doc.status,
            lastActive: doc.lastActive,
          }));
        } catch (error) {
          if (error instanceof AppwriteException) {
            throw new TeamApiError(
              `Appwrite API error: ${error.message}`,
              error
            );
          }
          throw new TeamApiError(
            'Failed to fetch team members',
            error
          );
        }
      },
      enabled: !!userId && (options.enabled ?? true),
      staleTime: options.staleTime ?? 1000 * 60 * 5, // 5 minutes
      gcTime: options.gcTime ?? 1000 * 60 * 10, // 10 minutes
      retry: options.retry ?? 2,
    });

  const useTeamsQuery = (
    userId: string,
    options: TeamQueryOptions = {}
  ): UseQueryResult<{ id: string, name: string }[], TeamApiError> => 
    useQuery({
      queryKey: ['teams', userId],
      queryFn: async () => {
        if (!userId) {
          throw new TeamApiError('User ID is required', null);
        }
        
        try {
          // Get all projects where user is a team member
          const projectsResponse = await databases.listDocuments(
            APPWRITE_CONFIG.databaseId,
            APPWRITE_CONFIG.collections.projects,
            [Query.equal('teamMembers', userId)]
          );
          
          return projectsResponse.documents.map(project => ({
            id: project.$id,
            name: project.name
          }));
        } catch (error) {
          if (error instanceof AppwriteException) {
            throw new TeamApiError(
              `Appwrite API error: ${error.message}`,
              error
            );
          }
          throw new TeamApiError(
            'Failed to fetch teams',
            error
          );
        }
      },
      enabled: !!userId && (options.enabled ?? true),
      staleTime: options.staleTime ?? 1000 * 60 * 5, // 5 minutes
      gcTime: options.gcTime ?? 1000 * 60 * 10, // 10 minutes
      retry: options.retry ?? 2,
    });

  return {
    useTeamMembersQuery,
    useTeamsQuery
  };
}


