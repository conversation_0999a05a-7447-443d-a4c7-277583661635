import { Project } from '@/lib/types';
import { format } from 'date-fns';
import { Progress } from '@/components/ui/progress';
import { FileText, Calendar, Users, Clock } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface ProjectCardProps {
  project: Project;
  student?: { id: string; name: string; profileImage?: string };
  onClick?: () => void;
  showProgress?: boolean;
}

export function ProjectCard({ project, student, onClick, showProgress = true }: ProjectCardProps) {
  const progress = project.documents?.length > 0
    ? Math.round((project.documents.filter(d => typeof d === 'object' ? d.status === 'approved' : false).length / project.documents.length) * 100)
    : 0;

  const deadline = project.deadline ? new Date(project.deadline) : null;
  const isOverdue = deadline && deadline < new Date();

  return (
    <div
      className="dashboard-card cursor-pointer group h-full flex flex-col relative overflow-hidden"
      onClick={onClick}
    >
      {/* Status indicator bar */}
      <div
        className="absolute top-0 left-0 w-1 h-full"
        style={{ backgroundColor: getStatusColor(project.status) }}
      />

      <div className="dashboard-card-header">
        <div className="flex justify-between items-start">
          <h3 className="text-lg font-semibold line-clamp-1 group-hover:text-primary transition-colors text-card-foreground">
            {project.title}
          </h3>
          <span className={`status-indicator ${getStatusClass(project.status)}`}>
            {formatStatus(project.status)}
          </span>
        </div>

        {student && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground mt-2">
            <Avatar className="h-5 w-5">
              <AvatarImage src={student.profileImage} alt={student.name} />
              <AvatarFallback>{student.name?.slice(0, 2).toUpperCase()}</AvatarFallback>
            </Avatar>
            <span className="line-clamp-1">{student.name}</span>
          </div>
        )}
      </div>

      <div className="dashboard-card-content flex-1">
        <p className="text-sm text-muted-foreground line-clamp-2 mb-4">
          {project.description}
        </p>

        {showProgress && (
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-xs">
              <span className="text-muted-foreground">Progress</span>
              <span className="font-medium">{progress}%</span>
            </div>
            <Progress
              value={progress}
              className={`h-1.5 ${progress === 100 ? "[&>.bg-primary]:bg-green-500" : ""}`}
            />
          </div>
        )}

        <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
          {deadline && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span className={isOverdue ? "text-red-500 font-medium" : ""}>
                      {format(deadline, 'MMM d, yyyy')}
                    </span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{isOverdue ? 'Overdue' : 'Deadline'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          <div className="flex items-center gap-1">
            <FileText className="h-3 w-3" />
            <span>{project.documents?.length || 0} docs</span>
          </div>
        </div>
      </div>

      <div className="px-6 py-3 border-t border-border text-xs text-muted-foreground">
        <div className="flex justify-between items-center w-full">
          <div className="flex items-center gap-1">
            <Users className="h-3 w-3" />
            <span>{project.supervisorIds?.length || 0} supervisors</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>{format(new Date(project.lastActivity), 'MMM d')}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

function getStatusColor(status: string): string {
  switch (status) {
    case 'active': return '#10b981'; // green-500
    case 'completed': return '#3b82f6'; // blue-500
    case 'archived': return '#6b7280'; // gray-500
    case 'suspended': return '#ef4444'; // red-500
    case 'pending_supervisor': return '#f59e0b'; // amber-500
    default: return '#6b7280'; // gray-500
  }
}

function getStatusClass(status: string): string {
  switch (status) {
    case 'active': return 'status-active';
    case 'completed': return 'status-completed';
    case 'archived': return 'status-pending';
    case 'suspended': return 'status-overdue';
    case 'pending_supervisor': return 'status-pending';
    default: return 'status-pending';
  }
}

function formatStatus(status: string): string {
  return status.split('_').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
}


