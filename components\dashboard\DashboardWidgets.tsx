import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { User, Project } from '@/lib/types';
import {
  Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  PieChart, Pie, Cell, LineChart, Line
} from 'recharts';
import {
  Users, FileText, Clock, AlertCircle, CheckCircle,
  TrendingUp, Calendar, Activity
} from 'lucide-react';
import { format, subDays } from 'date-fns';
import { DashboardMetrics } from './DashboardMetrics';

interface DashboardWidgetsProps {
  projects: Project[];
  users: User[];
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

export function DashboardWidgets({ projects, users }: DashboardWidgetsProps) {
  // Calculate project statistics
  const activeProjects = projects.filter(p => p.status === 'active');
  const completedProjects = projects.filter(p => p.status === 'completed');
  const overdueProjects = projects.filter(p => {
    return p.status === 'active' && new Date(p.deadline) < new Date();
  });

  // Calculate activity trends (last 7 days)
  const activityTrend = Array.from({ length: 7 }, (_, i) => {
    const date = subDays(new Date(), i);
    const dateStr = format(date, 'MMM dd');
    const count = projects.filter(p => {
      if (!p.lastActivity) return false;
      try {
        const activityDate = new Date(p.lastActivity);
        if (isNaN(activityDate.getTime())) return false;
        return format(activityDate, "MMM dd") === dateStr;
      } catch {
        return false;
      }
    }).length;
    return { date: dateStr, activities: count };
  }).reverse();

  // Calculate department distribution
  const departmentStats = users.reduce((acc, user) => {
    acc[user.department || 'Unassigned'] = (acc[user.department || 'Unassigned'] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const departmentData = Object.entries(departmentStats).map(([name, value]) => ({
    name,
    value
  }));

  return (
    <div className="space-y-8">
      {/* Quick Stats Cards using reusable component */}
      <DashboardMetrics
        projects={projects}
        users={users}
        variant="overview"
      />

      {/* Charts Section */}
      <div className="dashboard-grid-2">
        {/* Activity Trend Chart */}
        <div className="dashboard-card">
        <div className="dashboard-card-header">
          <h3 className="dashboard-section-title">Activity Trend</h3>
        </div>
        <div className="dashboard-card-content">
          <div className="h-[200px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={activityTrend}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis dataKey="date" className="text-xs" />
                <YAxis className="text-xs" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                    boxShadow: '0 4px 12px hsl(var(--dashboard-card-shadow))'
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="activities"
                  stroke="hsl(var(--primary))"
                  strokeWidth={3}
                  dot={{ fill: 'hsl(var(--primary))', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: 'hsl(var(--primary))', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

        {/* Department Distribution */}
        <div className="dashboard-card">
        <div className="dashboard-card-header">
          <h3 className="dashboard-section-title">Department Distribution</h3>
        </div>
        <div className="dashboard-card-content">
          <div className="h-[200px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={departmentData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  fill="#8884d8"
                  paddingAngle={5}
                  dataKey="value"
                >
                  {departmentData.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                    boxShadow: '0 4px 12px hsl(var(--dashboard-card-shadow))'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="grid grid-cols-2 gap-4 mt-4">
            {departmentData.map((dept, index) => (
              <div key={dept.name} className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: COLORS[index % COLORS.length] }}
                />
                <span className="text-sm text-card-foreground">{dept.name}: {dept.value}</span>
              </div>
            ))}
          </div>
        </div>
        </div>
      </div>
    </div>
  );
}
