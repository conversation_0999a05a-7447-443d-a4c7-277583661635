"use client";

import { Head<PERSON> } from "@/components/layout/Header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import React, {
  useState,
  useCallback,
  useMemo,
  memo,
  Suspense,
  useRef,
} from "react";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import {
  MoreVertical,
  ArrowLeft,
  Users,
  UserPlus,
  Search,
  Bell,
  User as UserIcon,
  X,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

import { useAuth } from "@/hooks/useAuth";
import { ChatContacts } from "@/components/chat/ChatContacts";
import { useContacts } from "@/hooks/useContacts";
import { ChatMessage, User } from "@/lib/types";
import { MessageSkeleton } from "@/components/chat/Message";

import { useToast } from "@/hooks/useToast";
import { BaseChatView } from "@/components/chat/BaseChatView";
import { ChatInput } from "@/components/chat/ChatInput";
import { useChat } from "@/hooks/useChat";
import { useRealtimeChat } from "@/hooks/useRealtimeChat";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";

// Constants
const LOADING_SKELETON_COUNT = 5;
const MOBILE_BREAKPOINT = "md";

// Types
interface ChatViewProps {
  selectedUserId: string;
  userId: string;
  selectedContact: User;
  messages: ChatMessage[];
  hasMore: boolean;
  isLoadingMore: boolean;
  isLoadingChat: boolean;
  loadMore: () => void;
  sendMessage: (message: string) => Promise<void>;
  onBack: () => void;
  isRecipientTyping?: boolean;
  onReplyToMessage?: (message: ChatMessage) => void;
  onDeleteMessage?: (messageId: string) => void;
  setForceRenderKey?: React.Dispatch<React.SetStateAction<number>>; // For immediate re-render

  replyToMessage?: ChatMessage | null;
  onCancelReply?: () => void;
}

interface ChatHeaderProps {
  contact: User;
  onBack: () => void;
  isGroup?: boolean;
  memberCount?: number;
  onViewMembers?: () => void;
  onAddMember?: () => void;
  onSearchClick?: () => void;
}

// UI Components
const EmptyStateMessage = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-full">
    <div className="text-muted-foreground text-center">
      <p>{message}</p>
    </div>
  </div>
);

const ErrorDisplay = ({
  message,
  error,
}: {
  message: string;
  error?: Error;
}) => {
  const [showDetails, setShowDetails] = useState(false);

  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-destructive text-center max-w-md p-4">
        <p className="text-lg font-semibold mb-2">{message}</p>

        {error && (
          <p className="text-sm text-muted-foreground mb-4">
            {error.message || "Unknown error occurred"}
          </p>
        )}

        <div className="flex flex-col sm:flex-row justify-center gap-2 mt-4">
          <Button variant="default" onClick={() => window.location.reload()}>
            Refresh Page
          </Button>

          {error && (
            <Button
              variant="outline"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? "Hide Details" : "Show Details"}
            </Button>
          )}
        </div>

        {showDetails && error && (
          <div className="mt-4 p-2 bg-muted rounded text-left overflow-auto max-h-32 text-xs">
            <pre>{error.stack || JSON.stringify(error, null, 2)}</pre>
          </div>
        )}
      </div>
    </div>
  );
};

const ChatErrorBoundary = ({ children }: { children: React.ReactNode }) => (
  <ErrorBoundary>{children}</ErrorBoundary>
);

const LoadingSkeleton = memo(({ count }: { count: number }) => (
  <div className="space-y-4">
    {Array.from({ length: count }).map((_, i) => (
      <MessageSkeleton key={i} />
    ))}
  </div>
));
LoadingSkeleton.displayName = "LoadingSkeleton";

const ChatLoading = () => (
  <div className="flex items-center justify-center h-full">
    <LoadingSkeleton count={LOADING_SKELETON_COUNT} />
  </div>
);

const ChatHeader = memo<ChatHeaderProps>(
  ({
    contact,
    onBack,
    isGroup = false,
    memberCount = 0,
    onViewMembers,
    onAddMember,
    onSearchClick,
  }) => (
    <div className="chat-header">
      <div className="flex items-center gap-3 py-2 px-4 w-full">
        <Button
          variant="ghost"
          size="icon"
          className={`${MOBILE_BREAKPOINT}:hidden mr-2 text-[hsl(var(--chat-timestamp))] hover:bg-[hsl(var(--accent))] rounded-full p-2 h-auto w-auto transition-colors`}
          onClick={onBack}
          aria-label="Back"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>

        {contact && (
          <>
            <Avatar className="h-10 w-10 shadow-sm">
              {isGroup ? (
                <div className="bg-[hsl(var(--primary))] h-full w-full flex items-center justify-center text-[hsl(var(--primary-foreground))]">
                  <Users className="h-5 w-5" />
                </div>
              ) : (
                <>
                  <AvatarImage src={contact.profileImage} alt={contact.name} />
                  <AvatarFallback className="bg-[hsl(var(--accent))] text-[hsl(var(--accent-foreground))] font-medium">
                    {contact.name[0].toUpperCase()}
                  </AvatarFallback>
                </>
              )}
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="font-semibold text-[hsl(var(--foreground))] leading-none truncate">
                {contact.name}
              </p>
              {isGroup ? (
                <p className="text-xs text-[hsl(var(--chat-timestamp))] mt-0.5">
                  {memberCount} members
                </p>
              ) : (
                <div className="text-[hsl(var(--chat-timestamp))] text-xs mt-0.5">
                  {contact.status}
                </div>
              )}
            </div>

            <div className="flex items-center gap-2">
              <button
                className="text-[hsl(var(--chat-timestamp))] hover:bg-[hsl(var(--accent))] p-2 rounded-full transition-colors"
                onClick={onSearchClick}
                aria-label="Search in conversation"
              >
                <Search className="h-5 w-5" />
              </button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button className="text-[hsl(var(--chat-timestamp))] hover:bg-[hsl(var(--accent))] p-2 rounded-full transition-colors">
                    <MoreVertical className="h-5 w-5" />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {isGroup ? (
                    <>
                      <DropdownMenuItem onClick={onViewMembers}>
                        <Users className="h-4 w-4 mr-2" />
                        View members
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={onAddMember}>
                        <UserPlus className="h-4 w-4 mr-2" />
                        Add member
                      </DropdownMenuItem>
                    </>
                  ) : (
                    <>
                      <DropdownMenuItem>
                        <UserIcon className="h-4 w-4 mr-2" />
                        View profile
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Bell className="h-4 w-4 mr-2" />
                        Mute notifications
                      </DropdownMenuItem>
                    </>
                  )}
                  <DropdownMenuItem onClick={onSearchClick}>
                    <Search className="h-4 w-4 mr-2" />
                    Search in conversation
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </>
        )}
      </div>
    </div>
  )
);
ChatHeader.displayName = "ChatHeader";

// Optimized chat view component with improved performance
const ChatView = memo<ChatViewProps>(
  ({
    selectedUserId,
    userId,
    selectedContact,
    messages,
    hasMore,
    isLoadingMore,
    isLoadingChat,
    loadMore,
    onBack,
    isRecipientTyping,
    onReplyToMessage,
    onDeleteMessage,
    setForceRenderKey,
  }) => {
    // State for search functionality
    const [showSearch, setShowSearch] = useState(false);

    // Initialize real-time chat with immediate re-render callback
    const { isConnected, connectionStatus, reconnect } = useRealtimeChat({
      userId,
      recipientId: selectedUserId,
      enabled: true,
      onMessageReceived: useCallback(() => {
        // Force immediate re-render when realtime message is received
        if (setForceRenderKey) {
          setForceRenderKey((prev: number) => prev + 1);
          console.log("ChatView: Forced re-render due to realtime message");
        }
      }, [setForceRenderKey]),
      onMessageDeleted: useCallback(() => {
        // Force immediate re-render when realtime message is deleted
        if (setForceRenderKey) {
          setForceRenderKey((prev: number) => prev + 1);
          console.log(
            "ChatView: Forced re-render due to realtime message deletion"
          );
        }
      }, [setForceRenderKey]),
    });

    // Handle search click
    const handleSearchClick = useCallback(() => {
      setShowSearch(true);
    }, []);

    // Memoize header component to prevent unnecessary re-renders
    const headerComponent = useMemo(
      () => (
        <ChatHeader
          contact={selectedContact}
          onBack={onBack}
          onSearchClick={handleSearchClick}
        />
      ),
      [selectedContact, onBack, handleSearchClick]
    );

    // Chat input component with enhanced styling
    const inputComponent = useMemo(
      () => (
        <div className="chat-input-container p-2">
          <ChatInput
            recipientId={selectedUserId}
            userId={userId}
            placeholder="Type a message..."
          />
        </div>
      ),
      [selectedUserId, userId]
    );

    return (
      <BaseChatView
        messages={messages}
        userId={userId}
        hasMore={hasMore}
        isLoadingMore={isLoadingMore}
        isLoadingChat={isLoadingChat}
        loadMore={loadMore}
        isRecipientTyping={isRecipientTyping}
        typingUserName={selectedContact?.name}
        onReplyToMessage={onReplyToMessage}
        onDeleteMessage={onDeleteMessage}
        headerComponent={headerComponent}
        inputComponent={inputComponent}
        showSearch={showSearch}
        setShowSearch={setShowSearch}
        isRealtimeConnected={isConnected}
        connectionStatus={connectionStatus}
        onReconnect={reconnect}
      />
    );
  }
);
ChatView.displayName = "ChatView";

export default function ChatPage() {
  const { user } = useAuth();
  const userId = user?.id;
  const { toast } = useToast();
  const [selectedUserId, setSelectedUserId] = useState<string | undefined>(
    undefined
  );
  const [showMobileChat, setShowMobileChat] = useState(false);
  const [chatError, setChatError] = useState<Error | null>(null);

  const [searchTerm, setSearchTerm] = useState("");
  const searchInputRef = useRef<HTMLInputElement>(null);

  const {
    useChatMessages,
    sendMessage: sendChatMessage,
    addOptimisticMessage,
    deleteMessage,
  } = useChat();

  const {
    data: messagesData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isLoadingChat,
    isError: isMessagesError,
  } = useChatMessages(selectedUserId, userId);

  // Force re-render state for immediate message updates
  const [forceRenderKey, setForceRenderKey] = useState(0);

  // Optimize message processing with better memoization and force re-render capability
  const messages = useMemo(
    () => {
      // Handle case when messagesData is undefined
      if (!messagesData) return [];

      // Type assertion to help TypeScript understand the structure
      const data = messagesData as {
        pages?: Array<{ messages?: ChatMessage[] }>;
      };

      // Check if pages exists and is an array
      if (!data.pages || !Array.isArray(data.pages)) return [];

      // Use a more efficient approach to flatten pages
      const allMessages: ChatMessage[] = [];

      // Safely iterate through pages
      data.pages.forEach((page: { messages?: ChatMessage[] }) => {
        if (page && page.messages) {
          allMessages.push(...page.messages);
        }
      });

      console.log("Messages memoization updated:", {
        totalMessages: allMessages.length,
        forceRenderKey,
        latestMessage: allMessages[allMessages.length - 1]?.id,
      });

      return allMessages;
    },
    [messagesData, forceRenderKey] // Added forceRenderKey to dependencies
  );

  // Get contacts for direct messages
  const { data: contacts, isLoading: isLoadingContacts } = useContacts(
    userId || ""
  );

  // Enhanced contact filtering with improved performance
  const filteredContacts = useMemo(() => {
    if (!contacts) return [];
    if (!searchTerm.trim()) return contacts;

    const normalizedSearchTerm = searchTerm.toLowerCase().trim();

    // Optimize search by using a more efficient filtering approach
    return contacts.filter((contact) => {
      // Check name match
      if (contact.name.toLowerCase().includes(normalizedSearchTerm)) {
        return true;
      }

      // Check email match if available
      if (
        contact.email &&
        contact.email.toLowerCase().includes(normalizedSearchTerm)
      ) {
        return true;
      }

      // Check ID match (useful for debugging or admin users)
      if (contact.id.toLowerCase().includes(normalizedSearchTerm)) {
        return true;
      }

      return false;
    });
  }, [contacts, searchTerm]);

  const selectedContact = useMemo(
    () => contacts?.find((c) => c.id === selectedUserId),
    [contacts, selectedUserId]
  );

  const handleSelectUser = useCallback(
    (userId: string) => {
      // Only update if the selected user has changed
      if (selectedUserId !== userId) {
        setSelectedUserId(userId);
        setShowMobileChat(true);
      }
    },
    [selectedUserId]
  );

  const handleBack = useCallback(() => {
    setShowMobileChat(false);
  }, []);

  const handleReplyToMessage = useCallback((message: ChatMessage) => {
    // Reply functionality not implemented in simplified version
    console.log("Reply to message:", message.id);
  }, []);

  const handleDeleteMessage = useCallback(
    async (messageId: string) => {
      try {
        // Find the message to check if it's a file message
        const messageToDelete = messages.find((msg) => msg.id === messageId);
        const isFileMessage =
          messageToDelete?.type === "file" && !!messageToDelete?.fileUrl;

        await deleteMessage.mutateAsync(messageId);

        // Show appropriate toast message
        if (isFileMessage) {
          toast({
            title: "Message and file deleted",
            description:
              "Your message and associated file have been deleted successfully",
          });
        } else {
          toast({
            title: "Message deleted",
            description: "Your message has been deleted successfully",
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete message. Please try again.",
          variant: "destructive",
        });
      }
    },
    [deleteMessage, toast, messages]
  );

  // Define the message sending function
  const handleSendMessage = useCallback(
    async (message: string) => {
      // Validate input data
      const trimmedMessage = message.trim();
      if (!userId || !selectedUserId || !trimmedMessage) {
        console.log("Missing required data for sending message:", {
          userId,
          selectedUserId,
          messageLength: trimmedMessage.length,
        });
        return;
      }

      // Add optimistic message to UI immediately
      addOptimisticMessage(userId, selectedUserId, trimmedMessage);

      try {
        // Send the message
        await sendChatMessage.mutateAsync({
          content: trimmedMessage,
          senderId: userId,
          recipientId: selectedUserId,
          type: "text",
        });
      } catch (error) {
        console.error("Error sending message:", error);

        // Check if this is a network error
        const isNetworkError =
          error instanceof Error &&
          (error.message.includes("network") ||
            error.message.includes("connection") ||
            error.message.includes("offline"));

        // Show toast with helpful information
        toast({
          title: "Message Failed",
          description: isNetworkError
            ? "Network issue detected. Please try again."
            : "Failed to send message. Please try again.",
          variant: "destructive",
        });

        // Update the chat error state for critical errors
        if (!isNetworkError) {
          setChatError(
            error instanceof Error ? error : new Error("Failed to send message")
          );
        }
      }
    },
    [
      userId,
      selectedUserId,
      addOptimisticMessage,
      sendChatMessage,
      toast,
      setChatError,
    ]
  );

  // Prepare the sign-in message UI but don't return early
  const signInMessage = (
    <div className="min-h-screen bg-background flex flex-col">
      <Header />
      <main className="flex-1 flex items-center justify-center">
        <div className="text-muted-foreground">
          Please sign in to access chat
        </div>
      </main>
    </div>
  );

  // Track any errors that occur during chat operations
  const showError = isMessagesError || chatError !== null;

  // Return sign-in message if no user
  if (!userId) {
    return signInMessage;
  }

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <Header />
      <main className="flex-1">
        <div className="container h-[calc(100vh-4rem)] py-2 sm:py-4 md:py-6 px-6">
          <div className="grid h-full gap-6 md:grid-cols-4">
            <div
              className={cn(
                "dashboard-card md:col-span-1 overflow-hidden",
                showMobileChat ? "hidden md:block" : "block"
              )}
            >
              <div className="dashboard-card-content">
                {/* Enhanced search input with clear button */}
                <div className="relative mb-4">
                  <Input
                    placeholder="Search contacts..."
                    className="pl-8 pr-8 rounded-lg border-0 shadow-sm"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    ref={searchInputRef}
                    aria-label="Search"
                  />
                  <Search className="h-4 w-4 absolute left-2.5 top-2.5 text-muted-foreground" />

                  {/* Clear button - only show when there's text */}
                  {searchTerm && (
                    <button
                      className="h-6 w-6 absolute right-1 top-1.5 rounded-md hover:bg-muted transition-colors flex items-center justify-center"
                      onClick={() => {
                        setSearchTerm("");
                        searchInputRef.current?.focus();
                      }}
                      aria-label="Clear search"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  )}
                </div>

                <Suspense fallback={<LoadingSkeleton count={3} />}>
                  <ChatContacts
                    contacts={filteredContacts}
                    selectedContact={selectedContact}
                    onSelectContact={handleSelectUser}
                    isLoading={isLoadingContacts}
                  />
                </Suspense>
              </div>
            </div>

            <div
              className={cn(
                "dashboard-card md:col-span-3 flex flex-col overflow-hidden",
                showMobileChat ? "block" : "hidden md:flex"
              )}
            >
              <ChatErrorBoundary>
                <Suspense fallback={<ChatLoading />}>
                  {showError ? (
                    <ErrorDisplay
                      message="An error occurred loading chat data. Please try refreshing the page."
                      error={
                        chatError ||
                        (isMessagesError
                          ? new Error("Failed to fetch messages")
                          : undefined)
                      }
                    />
                  ) : selectedUserId && selectedContact ? (
                    <ChatView
                      selectedUserId={selectedUserId}
                      userId={userId}
                      selectedContact={selectedContact}
                      messages={messages}
                      hasMore={!!hasNextPage}
                      isLoadingMore={isFetchingNextPage}
                      isLoadingChat={isLoadingChat}
                      loadMore={() => fetchNextPage()}
                      sendMessage={handleSendMessage}
                      onBack={handleBack}
                      onReplyToMessage={handleReplyToMessage}
                      onDeleteMessage={handleDeleteMessage}
                      setForceRenderKey={setForceRenderKey}
                    />
                  ) : (
                    <EmptyStateMessage
                      message={
                        contacts && contacts.length > 0
                          ? "Select a contact to start chatting"
                          : "No contacts found. Add contacts to start chatting."
                      }
                    />
                  )}
                </Suspense>
              </ChatErrorBoundary>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
