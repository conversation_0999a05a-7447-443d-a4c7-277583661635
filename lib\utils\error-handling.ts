import { AppwriteException } from "appwrite";
import { toast } from "@/hooks/useToast";

export type ErrorSeverity = "error" | "warning" | "info";

export interface ErrorOptions {
  severity?: ErrorSeverity;
  showToast?: boolean;
  context?: string;
  originalError?: unknown;
  retry?: () => Promise<any>;
}

export class AppError extends Error {
  public severity: ErrorSeverity;
  public originalError?: unknown;

  constructor(
    message: string,
    options: ErrorOptions = {},
    originalError?: unknown
  ) {
    super(message);
    this.name = "AppError";
    this.severity = options.severity || "error";
    this.originalError = originalError;

    if (options.showToast) {
      toast({
        title: options.context || "Error",
        description: message,
        variant: options.severity === "warning" ? "default" : "destructive",
      });
    }
  }

  static fromUnknown(
    error: unknown,
    defaultMessage: string,
    options: ErrorOptions = {}
  ): AppError {
    return handleAppwriteError(error, defaultMessage, options);
  }
}

// Consolidated error handling function
export function handleAppwriteError(
  error: unknown,
  defaultMessage = "An error occurred",
  options: ErrorOptions = {}
): AppError {
  if (error instanceof AppwriteException) {
    let message = defaultMessage;
    let severity: ErrorSeverity = options.severity || "error";

    switch (error.code) {
      case 401:
        message = "Authentication required";
        break;
      case 403:
        message = "You don't have permission to perform this action";
        break;
      case 404:
        message = "The requested resource was not found";
        break;
      case 429:
        message = "Too many requests, please try again later";
        severity = "warning";
        break;
      default:
        message = error.message || defaultMessage;
    }

    return new AppError(message, { ...options, severity }, error);
  }

  if (error instanceof Error) {
    return new AppError(error.message, options, error);
  }

  return new AppError(defaultMessage, options, error);
}

// Simple version that just returns the error message as string
export function getErrorMessage(
  error: unknown,
  defaultMessage = "An error occurred"
): string {
  if (error instanceof Error) {
    return error.message;
  }
  return defaultMessage;
}

/**
 * Wraps an async function with standardized error handling
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  options: ErrorOptions = {}
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    throw AppError.fromUnknown(error, "Operation failed", options);
  }
}
