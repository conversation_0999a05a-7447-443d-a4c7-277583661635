import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardDescription } from '@/components/ui/card';
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  <PERSON><PERSON>hart, Pie, Cell, Legend, LineChart, Line, AreaChart, Area
} from 'recharts';
import { Project, User } from '@/lib/types';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { useState, useMemo } from 'react';
import { generateAnalyticsData } from '@/lib/utils/analytics';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Download, RefreshCw } from 'lucide-react';
import { EnhancedProjectsByMonth } from './EnhancedProjectsByMonth';

interface ProjectAnalyticsProps {
  projects: Project[];
  supervisors: User[];
}

// Define colors for status indicators
const STATUS_COLORS = {
  active: '#10b981',
  completed: '#3b82f6',
  archived: '#6b7280',
  suspended: '#ef4444',
  pending_supervisor: '#f59e0b'
};

export function ProjectAnalytics({ projects, supervisors }: ProjectAnalyticsProps) {
  const [timeRange, setTimeRange] = useState<'month' | 'quarter' | 'year'>('month');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Generate analytics data from projects and supervisors
  const data = useMemo(() =>
    generateAnalyticsData(projects, supervisors, timeRange),
    [projects, supervisors, timeRange]
  );

  // Handle refresh animation
  const handleRefresh = () => {
    setIsRefreshing(true);
    setTimeout(() => setIsRefreshing(false), 750);
  };

  // Handle export to CSV
  const handleExport = () => {
    // Create CSV content
    const csvContent = [
      // Headers
      ['Category', 'Value'],
      ['Total Projects', data.totalProjects],
      ['Active Projects', data.projectsByStatus.active],
      ['Completed Projects', data.projectsByStatus.completed],
      ['Archived Projects', data.projectsByStatus.archived],
      ['Suspended Projects', data.projectsByStatus.suspended],
      ['Pending Supervisor', data.projectsByStatus.pending_supervisor],
      ['Average Completion Time (days)', data.averageCompletionTime],
      [''],
      ['Projects by Month'],
      ['Month', 'Count'],
      ...data.projectsByMonth.map(item => [item.month, item.count]),
      [''],
      ['Supervisor Workload'],
      ['Supervisor', 'Project Count'],
      ...data.supervisorLoad.map(item => [item.supervisorName, item.projectCount]),
      [''],
      ['Project Trends'],
      ['Period', 'New Projects', 'Completed Projects'],
      ...data.projectTrends.map(item => [item.period, item.newProjects, item.completedProjects])
    ].map(row => row.join(',')).join('\n');

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `project-analytics-${timeRange}-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (!data) return <AnalyticsLoadingSkeleton />;

  const statusData = Object.entries(data.projectsByStatus).map(([status, count]) => ({
    name: formatStatus(status),
    value: count,
    color: STATUS_COLORS[status as keyof typeof STATUS_COLORS] || '#6b7280'
  }));

  const projectTrends = data.projectTrends || [];

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h2 className="text-2xl font-bold tracking-tight">Project Analytics</h2>

        <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
          <Tabs value={timeRange} onValueChange={(v) => setTimeRange(v as any)} className="w-full sm:w-[400px]">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="month">Last Month</TabsTrigger>
              <TabsTrigger value="quarter">Last Quarter</TabsTrigger>
              <TabsTrigger value="year">Last Year</TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={handleRefresh}
              className="h-10 w-10"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={handleExport}
              className="h-10 w-10"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
            {projectTrends.length > 0 && (
              <Badge variant="outline" className="text-xs font-normal">
                +{projectTrends[projectTrends.length - 1].newProjects} new
              </Badge>
            )}
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline">
              <div className="text-3xl font-bold">{data.totalProjects}</div>
              <div className="ml-2 text-sm text-muted-foreground">projects</div>
            </div>
            <div className="mt-2 h-1 w-full bg-muted overflow-hidden rounded-full">
              <div
                className="h-full bg-primary rounded-full"
                style={{ width: '100%' }}
              />
            </div>
            <p className="mt-2 text-xs text-muted-foreground">
              {projectTrends.length > 0 &&
                `${projectTrends[projectTrends.length - 1].newProjects} new projects in the last period`}
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: STATUS_COLORS.active }}
            />
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline">
              <div className="text-3xl font-bold">{data.projectsByStatus.active}</div>
              <div className="ml-2 text-sm text-muted-foreground">projects</div>
            </div>
            <div className="mt-2 h-1 w-full bg-muted overflow-hidden rounded-full">
              <div
                className="h-full rounded-full"
                style={{
                  width: `${(data.projectsByStatus.active / data.totalProjects) * 100}%`,
                  backgroundColor: STATUS_COLORS.active
                }}
              />
            </div>
            <p className="mt-2 text-xs text-muted-foreground">
              {((data.projectsByStatus.active / data.totalProjects) * 100).toFixed(1)}% of total projects
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed Projects</CardTitle>
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: STATUS_COLORS.completed }}
            />
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline">
              <div className="text-3xl font-bold">{data.projectsByStatus.completed}</div>
              <div className="ml-2 text-sm text-muted-foreground">projects</div>
            </div>
            <div className="mt-2 h-1 w-full bg-muted overflow-hidden rounded-full">
              <div
                className="h-full rounded-full"
                style={{
                  width: `${(data.projectsByStatus.completed / data.totalProjects) * 100}%`,
                  backgroundColor: STATUS_COLORS.completed
                }}
              />
            </div>
            <p className="mt-2 text-xs text-muted-foreground">
              {((data.projectsByStatus.completed / data.totalProjects) * 100).toFixed(1)}% of total projects
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Completion Time</CardTitle>
            <Badge variant="outline" className="text-xs font-normal">
              {data.projectsByStatus.completed} completed
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline">
              <div className="text-3xl font-bold">{data.averageCompletionTime}</div>
              <div className="ml-2 text-sm text-muted-foreground">days</div>
            </div>
            <div className="mt-2 flex items-center gap-1">
              <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
              <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
              <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
              <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
              <div className="h-1.5 w-1.5 rounded-full bg-muted"></div>
            </div>
            <p className="mt-2 text-xs text-muted-foreground">
              Average time from start to completion
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Projects by Month - Full Width */}
      <EnhancedProjectsByMonth
        data={data.projectsByMonth}
        className="col-span-full"
      />

      <div className="grid gap-4 md:grid-cols-2">

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2">
              <span>Project Status Distribution</span>
              <Badge variant="outline" className="ml-2 text-xs font-normal">
                {data.totalProjects} Total
              </Badge>
            </CardTitle>
            <CardDescription>Breakdown by current status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                  <defs>
                    {Object.entries(STATUS_COLORS).map(([status, color]) => (
                      <linearGradient key={status} id={`color-${status}`} x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor={color} stopOpacity={0.9}/>
                        <stop offset="100%" stopColor={color} stopOpacity={0.7}/>
                      </linearGradient>
                    ))}
                  </defs>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={90}
                    innerRadius={50}
                    paddingAngle={3}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) =>
                      percent > 0.05 ? `${name} ${(percent * 100).toFixed(0)}%` : ''
                    }
                    animationDuration={1500}
                    animationBegin={200}
                    isAnimationActive={true}
                  >
                    {statusData.map((_, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={`url(#color-${Object.keys(STATUS_COLORS)[index % Object.keys(STATUS_COLORS).length]})`}
                        stroke="rgba(255,255,255,0.8)"
                        strokeWidth={2}
                      />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      borderRadius: '8px',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                      border: 'none',
                      padding: '8px 12px'
                    }}
                    formatter={(value: number) => {
                      const percentage = data.totalProjects > 0
                        ? ((value / data.totalProjects) * 100).toFixed(1)
                        : '0';
                      return [`${value} projects (${percentage}%)`, ''];
                    }}
                    labelStyle={{ fontWeight: 'bold', marginBottom: '4px' }}
                  />
                  <Legend
                    layout="horizontal"
                    verticalAlign="bottom"
                    align="center"
                    wrapperStyle={{ paddingTop: '20px' }}
                    iconType="circle"
                    iconSize={10}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="grid grid-cols-2 gap-2 mt-4 text-sm">
              {statusData.map((status) => (
                <div
                  key={status.name}
                  className="flex items-center gap-2 p-1.5 rounded-md hover:bg-muted/50 transition-colors cursor-default"
                >
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: status.color }}
                  />
                  <span className="text-muted-foreground">{status.name}:</span>
                  <span className="font-medium">{status.value}</span>
                  <span className="text-xs text-muted-foreground ml-auto">
                    {data.totalProjects > 0
                      ? `${((status.value / data.totalProjects) * 100).toFixed(1)}%`
                      : '0%'
                    }
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2">
              <span>Supervisor Workload</span>
              {data.supervisorLoad.length > 0 && (
                <Badge variant="outline" className="ml-2 text-xs font-normal">
                  {data.supervisorLoad.length} Supervisors
                </Badge>
              )}
            </CardTitle>
            <CardDescription>Projects per supervisor</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={data.supervisorLoad}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                >
                  <defs>
                    <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="#82ca9d" stopOpacity={0.9}/>
                      <stop offset="100%" stopColor="#82ca9d" stopOpacity={0.6}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
                  <XAxis
                    dataKey="supervisorName"
                    tick={{ fill: '#888888' }}
                    tickLine={{ stroke: '#888888' }}
                    axisLine={{ stroke: '#e5e7eb' }}
                  />
                  <YAxis
                    tick={{ fill: '#888888' }}
                    tickLine={{ stroke: '#888888' }}
                    axisLine={{ stroke: '#e5e7eb' }}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      borderRadius: '8px',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                      border: 'none',
                      padding: '8px 12px'
                    }}
                    formatter={(value: number) => [`${value} projects`, 'Project Count']}
                    labelStyle={{ fontWeight: 'bold', marginBottom: '4px' }}
                    cursor={{ fill: 'rgba(130, 202, 157, 0.1)' }}
                  />
                  <Bar
                    dataKey="projectCount"
                    fill="url(#barGradient)"
                    stroke="#82ca9d"
                    strokeWidth={1}
                    radius={[4, 4, 0, 0]}
                    barSize={30}
                    animationDuration={1500}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
            {data.supervisorLoad.length > 0 && (
              <div className="mt-4 flex justify-between items-center text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-[#82ca9d]" />
                  <span className="text-muted-foreground">Project distribution</span>
                </div>
                <div className="font-medium">
                  Avg: {data.supervisorLoad.length > 0
                    ? (data.supervisorLoad.reduce((sum, item) => sum + item.projectCount, 0) / data.supervisorLoad.length).toFixed(1)
                    : '0'
                  } projects/supervisor
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2">
              <span>Project Trends</span>
              {projectTrends.length > 0 && (
                <Badge variant="outline" className="ml-2 text-xs font-normal">
                  {timeRange === 'month' ? 'Monthly' : timeRange === 'quarter' ? 'Quarterly' : 'Yearly'}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>New vs completed projects</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={projectTrends}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                >
                  <defs>
                    <linearGradient id="newProjectsGradient" x1="0" y1="0" x2="1" y2="0">
                      <stop offset="0%" stopColor="#8884d8" stopOpacity={0.9}/>
                      <stop offset="100%" stopColor="#8884d8" stopOpacity={0.7}/>
                    </linearGradient>
                    <linearGradient id="completedProjectsGradient" x1="0" y1="0" x2="1" y2="0">
                      <stop offset="0%" stopColor="#82ca9d" stopOpacity={0.9}/>
                      <stop offset="100%" stopColor="#82ca9d" stopOpacity={0.7}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
                  <XAxis
                    dataKey="period"
                    tick={{ fill: '#888888' }}
                    tickLine={{ stroke: '#888888' }}
                    axisLine={{ stroke: '#e5e7eb' }}
                  />
                  <YAxis
                    tick={{ fill: '#888888' }}
                    tickLine={{ stroke: '#888888' }}
                    axisLine={{ stroke: '#e5e7eb' }}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      borderRadius: '8px',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                      border: 'none',
                      padding: '8px 12px'
                    }}
                    labelStyle={{ fontWeight: 'bold', marginBottom: '4px' }}
                    formatter={(value: number, name: string) => [
                      `${value} projects`,
                      name === 'newProjects' ? 'New Projects' : 'Completed Projects'
                    ]}
                  />
                  <Legend
                    wrapperStyle={{ paddingTop: '10px' }}
                    iconType="circle"
                    iconSize={8}
                    formatter={(value) => (
                      <span style={{ color: '#666', fontSize: '0.875rem' }}>
                        {value === 'newProjects' ? 'New Projects' : 'Completed Projects'}
                      </span>
                    )}
                  />
                  <Line
                    type="monotone"
                    dataKey="newProjects"
                    stroke="url(#newProjectsGradient)"
                    name="newProjects"
                    strokeWidth={3}
                    dot={{ r: 4, strokeWidth: 2, fill: '#fff', stroke: '#8884d8' }}
                    activeDot={{ r: 6, strokeWidth: 2, stroke: '#fff', fill: '#8884d8' }}
                    animationDuration={1500}
                  />
                  <Line
                    type="monotone"
                    dataKey="completedProjects"
                    stroke="url(#completedProjectsGradient)"
                    name="completedProjects"
                    strokeWidth={3}
                    dot={{ r: 4, strokeWidth: 2, fill: '#fff', stroke: '#82ca9d' }}
                    activeDot={{ r: 6, strokeWidth: 2, stroke: '#fff', fill: '#82ca9d' }}
                    animationDuration={1500}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
            {projectTrends.length > 0 && (
              <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-[#8884d8]" />
                    <span className="text-muted-foreground">New Projects</span>
                  </div>
                  <div className="font-medium mt-1">
                    Total: {projectTrends.reduce((sum, item) => sum + item.newProjects, 0)}
                  </div>
                </div>
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-[#82ca9d]" />
                    <span className="text-muted-foreground">Completed Projects</span>
                  </div>
                  <div className="font-medium mt-1">
                    Total: {projectTrends.reduce((sum, item) => sum + item.completedProjects, 0)}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function AnalyticsLoadingSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header skeleton */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <Skeleton className="h-8 w-48" />

        <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
          <Skeleton className="h-10 w-full sm:w-[400px]" />

          <div className="flex gap-2">
            <Skeleton className="h-10 w-10 rounded-md" />
            <Skeleton className="h-10 w-10 rounded-md" />
          </div>
        </div>
      </div>

      {/* Stats cards skeleton */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="overflow-hidden">
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts skeleton */}
      <div className="grid gap-4 md:grid-cols-2">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="overflow-hidden">
            <CardHeader>
              <Skeleton className="h-5 w-40 mb-2" />
              <Skeleton className="h-3 w-60" />
            </CardHeader>
            <CardContent>
              <div className="relative">
                <Skeleton className="h-[300px] w-full rounded-md" />
                {/* Add some fake chart elements to make it look more like a chart */}
                <div className="absolute inset-0 flex items-center justify-center opacity-10">
                  <div className="w-3/4 h-3/4 border-b border-l border-gray-300"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

function formatStatus(status: string): string {
  return status.split('_').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
}


