"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Users,
  FileText,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  TrendingUp,
  Bar<PERSON>hart3,
} from "lucide-react";
import { Project, User, UserRole } from "@/lib/types";
import { useRouter } from "next/navigation";
import { useAnalytics } from "@/hooks/useAnalytics";
import { PendingSupervisorProjects } from "@/components/projects/PendingSupervisorProjects";
import { RealTimeAnalytics } from "@/components/analytics/RealTimeAnalytics";
import { ManagerAnalyticsView } from "@/components/analytics/ManagerAnalyticsView";
import { DashboardMetrics } from "../DashboardMetrics";

interface ManagerDashboardContentProps {
  users: User[];
  projects: Project[];
  isLoading: boolean;
}

export function ManagerDashboardContent({
  users,
  projects,
  isLoading,
}: ManagerDashboardContentProps) {
  const router = useRouter();

  // Get real analytics data for manager insights
  const {
    analytics,
    isLoading: isLoadingAnalytics,
    error: analyticsError
  } = useAnalytics({
    timeRange: 'quarter',
    enableRealTime: true,
    includeExtendedAnalytics: true,
    filters: { userRole: 'all' } // Managers see all data
  });

  // Calculate metrics
  const metrics = {
    totalUsers: users.length,
    totalProjects: projects.length,
    activeProjects: projects.filter((p) => p.status === "active").length,
    completedProjects: projects.filter((p) => p.status === "completed").length,
    pendingSupervisorProjects: projects.filter((p) => p.status === "pending_supervisor").length,
  };

  // Calculate real trends from analytics data
  const trends = analytics ? {
    users: analytics.userEngagement.engagementRate - 85, // Compare to baseline
    projects: analytics.projectTrends.length > 1 ?
      ((analytics.projectTrends[analytics.projectTrends.length - 1].newProjects -
        analytics.projectTrends[analytics.projectTrends.length - 2].newProjects) /
        analytics.projectTrends[analytics.projectTrends.length - 2].newProjects) * 100 : 0,
    active: analytics.projectsByStatus.active > 0 ?
      ((analytics.projectsByStatus.active / analytics.totalProjects) * 100) - 60 : 0, // Compare to 60% baseline
    completion: analytics.averageCompletionTime < 90 ? 5.0 : -2.0, // Good if under 90 days
  } : {
    users: 0,
    projects: 0,
    active: 0,
    completion: 0,
  };

  // Count users by role
  const usersByRole = users.reduce((acc, user) => {
    const role = user.role;
    acc[role] = (acc[role] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);



  return (
    <div className="space-y-6">
      {/* Manager-specific stats using reusable component */}
      <DashboardMetrics
        projects={projects}
        users={users}
        variant="manager"
        loading={isLoading}
      />



      {/* Pending Supervisor Projects */}
      <PendingSupervisorProjects
        projects={projects}
        supervisors={users.filter((user) => user.role === "supervisor")}
        isLoading={isLoading}
      />

      {/* Manager Analytics Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Manager Analytics
              </CardTitle>
              <CardDescription>
                Comprehensive insights and real-time analytics for management oversight
              </CardDescription>
            </div>
            <Button
              variant="outline"
              onClick={() => router.push('/reports')}
              className="flex items-center gap-2"
            >
              <TrendingUp className="h-4 w-4" />
              View Full Reports
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {analyticsError ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>Unable to load analytics data</p>
              <p className="text-sm mt-1">{analyticsError}</p>
            </div>
          ) : (
            <ManagerAnalyticsView timeRange="year" />
          )}
        </CardContent>
      </Card>

    </div>
  );
}
