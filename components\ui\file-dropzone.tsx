import React, { useCallback, useState, useRef } from 'react';
import { cn } from '@/lib/utils';
import { Image, Video, X, FileText, Upload, Plus } from 'lucide-react';
import { FileIcon } from 'lucide-react'; // Renamed to avoid conflict with the File constructor
import { Button } from './button';
import { Progress } from './progress';

// Type definition for file with upload status
export interface FileWithStatus {
  id: string;
  file: File;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
  previewUrl?: string;
}

interface FileDropzoneProps {
  onFilesDrop: (files: File[]) => void;
  selectedFiles: FileWithStatus[];
  onRemoveFile: (fileId: string) => void;
  maxFiles?: number;
  maxSize?: number;
  accept?: string;
  className?: string;
  disabled?: boolean;
  compact?: boolean;
}

export function FileDropzone({
  onFilesDrop,
  selectedFiles,
  onRemoveFile,
  maxFiles = 5,
  maxSize = 10 * 1024 * 1024, // 10MB
  accept,
  className,
  disabled = false,
  compact = false,
}: FileDropzoneProps) {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection via input
  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled || !e.target.files || e.target.files.length === 0) return;

    const selectedInputFiles = Array.from(e.target.files);
    onFilesDrop(selectedInputFiles);

    // Reset the input value so the same file can be selected again if needed
    e.target.value = '';
  }, [disabled, onFilesDrop]);

  // Handle click on the dropzone to open file picker
  const handleClick = useCallback(() => {
    if (disabled) return;
    fileInputRef.current?.click();
  }, [disabled]);

  // Handle drag events
  const handleDragEnter = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) setIsDragging(true);
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) setIsDragging(true);
  }, [disabled]);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (disabled) return;

    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length === 0) return;

    // Check if adding these files would exceed the maximum
    if (selectedFiles.length + droppedFiles.length > maxFiles) {
      // This will be handled by the hook, but we could add a message here too
      onFilesDrop(droppedFiles);
      return;
    }

    onFilesDrop(droppedFiles);
  }, [disabled, maxFiles, onFilesDrop, selectedFiles.length]);

  // Get appropriate icon for file type
  const getFileIcon = (file: FileWithStatus) => {
    const fileType = file.file.type.split('/')[0];

    switch (fileType) {
      case 'image':
        return <Image className="h-5 w-5 text-primary" />;
      case 'video':
        return <Video className="h-5 w-5 text-primary" />;
      case 'audio':
        return <FileText className="h-5 w-5 text-primary" />;
      case 'application':
        if (file.file.type.includes('pdf')) {
          return <FileText className="h-5 w-5 text-primary" />;
        }
        return <FileIcon className="h-5 w-5 text-primary" />;
      default:
        return <FileIcon className="h-5 w-5 text-primary" />;
    }
  };

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  return (
    <div className={cn("w-full", className)}>
      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileInputChange}
        accept={accept}
        multiple={true}
        disabled={disabled}
      />

      {/* Drop zone area */}
      {(!compact || selectedFiles.length === 0) && (
        <div
          onClick={handleClick}
          onDragEnter={handleDragEnter}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          className={cn(
            "border-2 border-dashed rounded-md transition-colors",
            isDragging ? "border-primary bg-primary/5" : "border-muted",
            disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-primary/50 hover:bg-primary/5",
            compact ? "p-2" : "p-4"
          )}
        >
          <div className="flex flex-col items-center justify-center gap-2">
            <Upload className={cn(
              "text-muted-foreground",
              compact ? "h-4 w-4" : "h-6 w-6"
            )} />

            {!compact && (
              <>
                <p className="text-sm font-medium">
                  Drag files here or click to upload
                </p>
                <div className="flex items-center gap-2 mt-1">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="h-7 px-2 text-xs"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleClick();
                    }}
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Select Files
                  </Button>
                  <p className="text-xs text-muted-foreground">
                    {`Max ${maxFiles} files, up to ${maxSize / (1024 * 1024)}MB each`}
                  </p>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* File previews */}
      {selectedFiles.length > 0 && (
        <div className="mt-2 space-y-2">
          {/* Add more files button */}
          {selectedFiles.length < maxFiles && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="w-full flex items-center justify-center gap-2 h-9 mb-2 border-dashed hover:bg-primary/5"
              onClick={handleClick}
              disabled={disabled || selectedFiles.length >= maxFiles}
            >
              <Plus className="h-4 w-4" />
              <span>Add more files ({selectedFiles.length}/{maxFiles})</span>
            </Button>
          )}

          {selectedFiles.map((file) => (
            <div
              key={file.id}
              className={cn(
                "flex items-center gap-3 p-3 rounded-md border",
                file.status === 'error' ? "border-destructive bg-destructive/5" :
                file.status === 'success' ? "border-green-500/20 bg-green-500/5" :
                "border-border"
              )}
            >
              {/* Preview for images */}
              {file.previewUrl ? (
                <div className="h-12 w-12 rounded-md overflow-hidden flex-shrink-0 border border-border">
                  <img
                    src={file.previewUrl}
                    alt={file.file.name}
                    className="h-full w-full object-cover"
                  />
                </div>
              ) : (
                <div className="h-12 w-12 rounded-md bg-primary/10 flex items-center justify-center flex-shrink-0">
                  {getFileIcon(file)}
                </div>
              )}

              {/* File info */}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{file.file.name}</p>
                <div className="flex items-center gap-2">
                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(file.file.size)}
                  </p>
                  <span className="text-xs text-muted-foreground">
                    {file.file.type.split('/')[1]?.toUpperCase() || file.file.name.split('.').pop()?.toUpperCase()}
                  </span>
                  {file.status === 'error' && (
                    <p className="text-xs text-destructive">{file.error}</p>
                  )}
                  {file.status === 'success' && (
                    <p className="text-xs text-green-500">Uploaded</p>
                  )}
                </div>

                {/* Progress bar */}
                {(file.status === 'uploading' || file.status === 'success') && (
                  <div className="mt-2">
                    <Progress
                      value={file.progress}
                      className="h-1.5"
                      indicatorClassName={file.status === 'success' ? "bg-green-500" : undefined}
                    />
                    {file.status === 'uploading' && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {Math.round(file.progress)}% uploaded
                      </p>
                    )}
                  </div>
                )}
              </div>

              {/* Remove button */}
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8 rounded-full hover:bg-destructive/10 hover:text-destructive"
                onClick={() => onRemoveFile(file.id)}
                disabled={disabled}
                title="Remove file"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
