# Analytics Migration Guide: From Mock Data to Real Data

This guide shows how to migrate your analytics components from using mock data to real data through hooks.

## Overview

The analytics system has been enhanced to use real data from your comprehensive hook system instead of mock data. This provides:

✅ **Real-time data** from your actual database  
✅ **Comprehensive analytics** across all data types  
✅ **Automatic updates** when data changes  
✅ **Type-safe** analytics with proper error handling  
✅ **Performance optimized** with caching and query optimization  

## Migration Steps

### 1. Replace Mock Data with useAnalytics Hook

**Before (Mock Data):**
```typescript
// Old approach with mock data
const mockProjects = [
  { id: '1', title: 'Project 1', status: 'active' },
  // ... more mock data
];

const mockUsers = [
  { id: '1', name: 'User 1', role: 'student' },
  // ... more mock data
];

function AnalyticsComponent() {
  const analyticsData = generateAnalyticsData(mockProjects, mockUsers, 'month');
  
  return <ProjectAnalytics projects={mockProjects} supervisors={mockUsers} />;
}
```

**After (Real Data):**
```typescript
// New approach with real data hooks
import { useAnalytics } from '@/hooks/useAnalytics';

function AnalyticsComponent() {
  const { 
    analytics, 
    isLoading, 
    error, 
    refreshAnalytics,
    rawData 
  } = useAnalytics({
    timeRange: 'month',
    enableRealTime: true,
    includeExtendedAnalytics: true
  });

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  if (!analytics) return <NoDataMessage />;

  return (
    <ProjectAnalytics 
      projects={rawData.projects} 
      supervisors={rawData.supervisors} 
    />
  );
}
```

### 2. Update Existing Components

**Dashboard Page Migration:**
```typescript
// Before
import { generateAnalyticsData } from '@/lib/utils/analytics';

export default function DashboardPage() {
  const [projects, setProjects] = useState(mockProjects);
  const [users, setUsers] = useState(mockUsers);
  
  const analyticsData = useMemo(() =>
    generateAnalyticsData(projects, users, 'quarter'),
    [projects, users]
  );

  return (
    <div>
      <ProjectAnalytics projects={projects} supervisors={users} />
    </div>
  );
}

// After
import { useDashboardData } from '@/hooks/useDashboardData';
import { useAnalytics } from '@/hooks/useAnalytics';

export default function DashboardPage() {
  const dashboardData = useDashboardData();
  const analytics = useAnalytics({ timeRange: 'quarter' });

  return (
    <div>
      <ProjectAnalytics 
        projects={dashboardData.projects || []} 
        supervisors={dashboardData.supervisors || []} 
      />
      
      {/* Or use the new comprehensive analytics */}
      <RealTimeAnalytics 
        initialTimeRange="quarter"
        enableExtendedAnalytics={true}
      />
    </div>
  );
}
```

### 3. Enhanced Analytics Features

**Extended Analytics Data:**
```typescript
function ComprehensiveAnalytics() {
  const { analytics } = useAnalytics({
    includeExtendedAnalytics: true
  });

  if (!analytics) return null;

  return (
    <div>
      {/* Project Analytics */}
      <div>
        <h3>Projects: {analytics.totalProjects}</h3>
        <p>Completion Rate: {analytics.averageCompletionTime} days</p>
      </div>

      {/* Document Analytics */}
      <div>
        <h3>Documents: {analytics.documentAnalytics.totalDocuments}</h3>
        <p>Approval Rate: {analytics.documentAnalytics.approvalRate}%</p>
      </div>

      {/* Milestone Analytics */}
      <div>
        <h3>Milestones: {analytics.milestoneAnalytics.totalMilestones}</h3>
        <p>Overdue: {analytics.milestoneAnalytics.overdueMilestones}</p>
      </div>

      {/* User Engagement */}
      <div>
        <h3>Users: {analytics.userEngagement.totalUsers}</h3>
        <p>Active: {analytics.userEngagement.activeUsers}</p>
        <p>Engagement: {analytics.userEngagement.engagementRate}%</p>
      </div>
    </div>
  );
}
```

### 4. Real-Time Updates

**Enable Live Data Updates:**
```typescript
function LiveAnalytics() {
  const { 
    analytics, 
    refreshAnalytics, 
    lastUpdated,
    isFetching 
  } = useAnalytics({
    enableRealTime: true
  });

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(refreshAnalytics, 30000);
    return () => clearInterval(interval);
  }, [refreshAnalytics]);

  return (
    <div>
      <div className="flex items-center gap-2">
        <h2>Analytics</h2>
        {isFetching && <Spinner />}
        <button onClick={refreshAnalytics}>Refresh</button>
      </div>
      
      {lastUpdated && (
        <p className="text-sm text-muted-foreground">
          Last updated: {lastUpdated.toLocaleTimeString()}
        </p>
      )}
      
      {/* Analytics content */}
    </div>
  );
}
```

### 5. Role-Based Analytics

**Different Views for Different Roles:**
```typescript
function RoleBasedAnalytics() {
  const { user } = useAuth();
  
  const analyticsConfig = useMemo(() => {
    switch (user?.role) {
      case 'manager':
        return {
          timeRange: 'year' as const,
          filters: {},
          includeExtendedAnalytics: true
        };
      case 'supervisor':
        return {
          timeRange: 'quarter' as const,
          filters: { userRole: 'supervisor' },
          includeExtendedAnalytics: true
        };
      case 'student':
        return {
          timeRange: 'month' as const,
          filters: { userRole: 'student' },
          includeExtendedAnalytics: false
        };
      default:
        return { timeRange: 'month' as const };
    }
  }, [user?.role]);

  const analytics = useAnalytics(analyticsConfig);

  return (
    <RoleSpecificAnalyticsView 
      role={user?.role} 
      analytics={analytics} 
    />
  );
}
```

## Key Benefits of Migration

### 1. Real Data Accuracy
- **Before:** Static mock data that doesn't reflect actual system state
- **After:** Live data that updates automatically as users interact with the system

### 2. Comprehensive Insights
- **Before:** Limited to basic project analytics
- **After:** Full analytics across projects, documents, milestones, users, and notifications

### 3. Performance Optimization
- **Before:** No caching, potential for stale data
- **After:** Intelligent caching with React Query, optimized data fetching

### 4. Type Safety
- **Before:** Potential runtime errors with mock data mismatches
- **After:** Full TypeScript support with proper error handling

### 5. Real-Time Updates
- **Before:** Manual data refresh required
- **After:** Automatic updates with real-time capabilities

## Migration Checklist

- [ ] Replace mock data imports with `useAnalytics` hook
- [ ] Update component props to use real data from hooks
- [ ] Add proper loading and error states
- [ ] Configure appropriate time ranges for different views
- [ ] Set up role-based analytics filtering
- [ ] Enable real-time updates where appropriate
- [ ] Test analytics with actual data
- [ ] Update any hardcoded values or calculations
- [ ] Verify chart data sources are using real data
- [ ] Add export functionality for real data

## Common Migration Patterns

### Pattern 1: Simple Component Migration
```typescript
// Before
function SimpleAnalytics({ projects, users }) {
  const data = generateAnalyticsData(projects, users);
  return <Chart data={data} />;
}

// After
function SimpleAnalytics() {
  const { analytics, isLoading } = useAnalytics();
  
  if (isLoading) return <Skeleton />;
  return <Chart data={analytics} />;
}
```

### Pattern 2: Dashboard Integration
```typescript
// Before
function Dashboard() {
  const [data, setData] = useState(mockData);
  return <AnalyticsSection data={data} />;
}

// After
function Dashboard() {
  const dashboardData = useDashboardData();
  return <AnalyticsSection dashboardData={dashboardData} />;
}
```

### Pattern 3: Filtered Analytics
```typescript
// Before
function FilteredAnalytics({ filter }) {
  const filteredData = mockData.filter(item => item.status === filter);
  return <Analytics data={filteredData} />;
}

// After
function FilteredAnalytics({ filter }) {
  const { analytics } = useAnalytics({
    filters: { projectStatus: filter }
  });
  return <Analytics data={analytics} />;
}
```

## Testing Real Data Analytics

1. **Create test data** in your development environment
2. **Verify hook functionality** with actual database queries
3. **Test loading states** by simulating slow network conditions
4. **Test error states** by simulating API failures
5. **Verify real-time updates** by making changes in another browser tab
6. **Test role-based filtering** with different user accounts
7. **Validate chart data** matches expected calculations

## Performance Considerations

- Use appropriate `staleTime` and `cacheTime` for your data freshness needs
- Implement proper loading states to improve perceived performance
- Consider pagination for large datasets
- Use React.memo for expensive chart components
- Implement proper error boundaries for analytics components

This migration provides a robust, real-time analytics system that scales with your application and provides accurate insights based on actual user data.
