'use client';

import React, { useState, useC<PERSON>back, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';

import { DocumentEditor } from '@/components/documents/DocumentEditor';
import { useDocument } from '@/hooks/useDocument';
import { useAuth } from '@/hooks/useAuth';
import { useProject } from '@/hooks/useProject';
import { useToast } from '@/hooks/useToast';

import { DocumentType } from '@/lib/types';
import { Save, FileTex<PERSON>, AlertCircle, ArrowLeft, Eye } from 'lucide-react';

const DOCUMENT_TYPES = [
  { value: 'concept', label: 'Concept Paper' },
  { value: 'proposal', label: 'Thesis Proposal' },
  { value: 'thesis', label: 'Thesis' },
] as const;

// Auto-save configuration
const AUTO_SAVE_DELAY = 3000; // 3 seconds
const AUTO_SAVE_TOAST_DURATION = 2000; // 2 seconds

interface DocumentForm {
  title: string;
  type: DocumentType;
  projectId: string;
  content: string;
}

interface FormValidation {
  title: boolean;
  type: boolean;
  projectId: boolean;
  content: boolean;
  isValid: boolean;
}

interface ErrorStateProps {
  title: string;
  description: string;
  primaryAction: {
    label: string;
    onClick: () => void;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
  };
}

interface SaveStatusProps {
  hasUnsavedChanges: boolean;
  lastSaved: Date | null;
  autoSaveEnabled: boolean;
  onAutoSaveToggle: (enabled: boolean) => void;
}

type EditDocumentPageProps = {
  id: string;
};

// Reusable ErrorState component
function ErrorState({ title, description, primaryAction, secondaryAction }: ErrorStateProps) {
  return (
    <div className="min-h-screen bg-background">
      <main className="container py-6">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center space-y-6 max-w-md">
            <div className="mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center">
              <AlertCircle className="h-8 w-8 text-destructive" />
            </div>
            <div className="space-y-2">
              <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
              <p className="text-muted-foreground">{description}</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              {secondaryAction && (
                <Button
                  variant="outline"
                  onClick={secondaryAction.onClick}
                  className="min-w-[120px]"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  {secondaryAction.label}
                </Button>
              )}
              <Button
                onClick={primaryAction.onClick}
                className="min-w-[120px]"
              >
                <Eye className="h-4 w-4 mr-2" />
                {primaryAction.label}
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

// Save status indicator component
function SaveStatus({ hasUnsavedChanges, lastSaved, autoSaveEnabled, onAutoSaveToggle }: SaveStatusProps) {
  return (
    <div className="text-right">
      {/* Save status indicator */}
      <div className="flex items-center justify-end space-x-2 text-sm">
        {hasUnsavedChanges ? (
          <div className="flex items-center text-amber-600 dark:text-amber-400">
            <div className="w-2 h-2 bg-amber-500 rounded-full mr-2 animate-pulse"></div>
            Unsaved changes
          </div>
        ) : lastSaved ? (
          <div className="flex items-center text-green-600 dark:text-green-400">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            Saved {lastSaved.toLocaleTimeString()}
          </div>
        ) : null}
      </div>
      {/* Auto-save toggle */}
      <div className="flex items-center justify-end space-x-2 mt-1">
        <label className="text-xs text-muted-foreground cursor-pointer flex items-center">
          <input
            type="checkbox"
            checked={autoSaveEnabled}
            onChange={(e) => onAutoSaveToggle(e.target.checked)}
            className="mr-1 w-3 h-3"
          />
          Auto-save
        </label>
      </div>
    </div>
  );
}

// Enhanced loading skeleton component
function LoadingSkeleton() {
  return (
    <div className="min-h-screen bg-background">
      <main className="container py-6">
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <Skeleton className="h-8 w-64 mb-2" />
              <Skeleton className="h-4 w-48" />
            </div>
            <div className="text-right space-y-2">
              <Skeleton className="h-4 w-32 ml-auto" />
              <Skeleton className="h-4 w-24 ml-auto" />
            </div>
          </div>
        </div>

        {/* Info alert skeleton */}
        <div className="mb-6 p-4 border rounded-lg bg-blue-50 dark:bg-blue-950">
          <div className="flex items-start space-x-3">
            <Skeleton className="h-4 w-4 mt-0.5" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-3/4" />
            </div>
          </div>
        </div>

        <div className="space-y-6">
          {/* Document details card skeleton */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-3 w-48" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Editor skeleton */}
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex space-x-2">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <Skeleton key={i} className="h-8 w-8" />
                  ))}
                </div>
                <Skeleton className="h-64 w-full" />
                <div className="flex justify-end">
                  <Skeleton className="h-4 w-16" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action buttons skeleton */}
          <div className="flex justify-end space-x-2">
            <Skeleton className="h-10 w-20" />
            <Skeleton className="h-10 w-32" />
          </div>
        </div>
      </main>
    </div>
  );
}

export function EditDocumentPage({ id }: EditDocumentPageProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { user } = useAuth();

  // Document data
  const { getById: useDocumentQuery, update: updateDocument } = useDocument();
  const {
    data: document,
    isLoading: isLoadingDocument,
    error: documentError
  } = useDocumentQuery(id);

  // Projects data - get projects where user is student or supervisor
  const { useProjectsQuery } = useProject();
  const { data: allProjects = [] } = useProjectsQuery();

  // Filter projects to only show those where the user is a student or supervisor
  const projects = allProjects.filter((project) =>
    project.studentId === user?.id ||
    (project.supervisorIds && project.supervisorIds.includes(user?.id || ''))
  );

  // Form state
  const [form, setForm] = useState<DocumentForm>({
    title: '',
    type: 'concept',
    projectId: '',
    content: ''
  });

  // Find the current project for this document
  const currentProject = projects.find(project => project.id === form.projectId);

  const [formErrors, setFormErrors] = useState<FormValidation>({
    title: true,
    type: true,
    projectId: true,
    content: true,
    isValid: false
  });

  const [showValidationErrors, setShowValidationErrors] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);

  // Initialize form with document data
  useEffect(() => {
    if (document) {
      setForm({
        title: document.title,
        type: document.type,
        projectId: document.projectId,
        content: document.content || ''
      });
      setLastSaved(document.lastModified ? new Date(document.lastModified) : null);
      setHasUnsavedChanges(false);
    }
  }, [document]);

  // Auto-save functionality
  useEffect(() => {
    if (!autoSaveEnabled || !hasUnsavedChanges || !document || isSubmitting) return;

    const autoSaveTimer = setTimeout(async () => {
      // Only auto-save if we have minimum required fields for draft
      if (form.title.trim()) {
        try {
          await updateDocument.mutateAsync({
            id: document.id,
            title: form.title,
            type: form.type,
            content: form.content,
            status: 'draft', // Always keep as draft for auto-save
          });

          setLastSaved(new Date());
          setHasUnsavedChanges(false);

          // Show subtle success message for auto-save
          toast({
            title: "Auto-saved",
            description: "Your changes have been automatically saved",
            duration: AUTO_SAVE_TOAST_DURATION,
          });
        } catch (error) {
          console.error('Auto-save failed:', error);
          // Don't show error toast for auto-save failures to avoid being intrusive
        }
      }
    }, AUTO_SAVE_DELAY);

    return () => clearTimeout(autoSaveTimer);
  }, [form, hasUnsavedChanges, autoSaveEnabled, document, isSubmitting, updateDocument, toast]);

  // Validate form fields (for draft saves, only title is required since project is auto-filled)
  const validateForm = useCallback(() => {
    const errors: FormValidation = {
      title: !!form.title.trim(),
      type: !!form.type,
      projectId: !!form.projectId, // Always true since it's auto-filled from document
      content: !!form.content.trim(),
      isValid: false
    };

    // For draft saves, only title is required (project is auto-filled)
    errors.isValid = errors.title;

    setFormErrors(errors);
    return errors.isValid;
  }, [form]);

  // Handle form submission (now only saves as draft)
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (isSubmitting) return;

    setShowValidationErrors(true);

    // Only require title for draft saves (project is auto-filled)
    const isDraftValid = form.title.trim();

    if (!isDraftValid) {
      toast({
        title: "Validation Error",
        description: "Please provide a title to save as draft",
        variant: "destructive"
      });
      return;
    }

    // Validate user permissions
    if (!user?.id || !document) {
      toast({
        title: "Error",
        description: "You don't have permission to edit this document",
        variant: "destructive"
      });
      return;
    }

    // Only allow editing of draft documents by their owner
    if (document.status !== 'draft' || document.studentId !== user.id) {
      toast({
        title: "Error",
        description: "You can only edit your own draft documents",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Always save as draft
      await updateDocument.mutateAsync({
        id: document.id,
        title: form.title,
        type: form.type,
        content: form.content,
        status: 'draft',
      });

      // Update local state
      setLastSaved(new Date());
      setHasUnsavedChanges(false);

      toast({
        title: "Success",
        description: "Document saved as draft successfully"
      });

      // Stay on edit page for continued editing
      setShowValidationErrors(false);
    } catch (error) {
      console.error('Error updating document:', error);
      toast({
        variant: "destructive",
        title: "Save failed",
        description: "There was an error saving your document. Please try again."
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [form, toast, user, document, updateDocument]);

  const updateFormField = useCallback(<K extends keyof DocumentForm>(
    field: K,
    value: DocumentForm[K]
  ) => {
    setForm(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true); // Mark as having unsaved changes

    // Validate form after field update (with slight delay to ensure state is updated)
    setTimeout(() => validateForm(), 0);
  }, [validateForm]);



  // Loading state
  if (isLoadingDocument) {
    return <LoadingSkeleton />;
  }

  // Error state
  if (documentError || !document) {
    return (
      <ErrorState
        title="Document Not Found"
        description="The document you're trying to edit doesn't exist or you don't have permission to edit it."
        primaryAction={{
          label: "View All Documents",
          onClick: () => router.push('/documents')
        }}
        secondaryAction={{
          label: "Go Back",
          onClick: () => router.back()
        }}
      />
    );
  }

  // Permission check
  if (document.status !== 'draft' || document.studentId !== user?.id) {
    return (
      <ErrorState
        title="Cannot Edit Document"
        description="You can only edit your own draft documents. This document is either not a draft or doesn't belong to you."
        primaryAction={{
          label: "View Document",
          onClick: () => router.push(`/documents/${document.id}`)
        }}
        secondaryAction={{
          label: "Go Back",
          onClick: () => router.back()
        }}
      />
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <main className="container py-6">
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Edit Document</h1>
              <p className="text-muted-foreground">Make changes to your draft document</p>
            </div>
            <SaveStatus
              hasUnsavedChanges={hasUnsavedChanges}
              lastSaved={lastSaved}
              autoSaveEnabled={autoSaveEnabled}
              onAutoSaveToggle={setAutoSaveEnabled}
            />
          </div>
        </div>

        {/* Draft editing info */}
        <Alert className="mb-6 border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
          <FileText className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          <AlertTitle className="text-blue-800 dark:text-blue-200">Draft Editing Mode</AlertTitle>
          <AlertDescription className="text-blue-700 dark:text-blue-300">
            <div className="space-y-1">
              <p>You can save this document as a draft multiple times and continue editing.</p>
              {autoSaveEnabled && (
                <p className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                  Auto-save is enabled and will save your changes every {AUTO_SAVE_DELAY / 1000} seconds.
                </p>
              )}
              <p>All changes will be saved as draft. To submit for review, use the document workflow on the document view page.</p>
            </div>
          </AlertDescription>
        </Alert>

        {/* Show validation alert if there are errors */}
        {showValidationErrors && !formErrors.isValid && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Validation Error</AlertTitle>
            <AlertDescription>
              <p className="mb-2">Please fix the following errors to save as draft:</p>
              <ul className="list-disc pl-5 space-y-1">
                {!formErrors.title && <li>Document title is required</li>}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Document Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-2">
                  <Label
                    htmlFor="title"
                    className={`font-medium ${!formErrors.title && showValidationErrors ? "text-destructive" : ""}`}
                  >
                    Document Title <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="title"
                    value={form.title}
                    onChange={e => updateFormField('title', e.target.value)}
                    placeholder="Enter a descriptive title for your document"
                    required
                    className={`transition-colors ${!formErrors.title && showValidationErrors ? "border-destructive focus:border-destructive" : ""}`}
                    aria-invalid={!formErrors.title && showValidationErrors}
                    aria-describedby={!formErrors.title && showValidationErrors ? "title-error" : undefined}
                    disabled={isSubmitting}
                  />
                  {!formErrors.title && showValidationErrors && (
                    <p id="title-error" className="text-sm text-destructive flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      Title is required
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label className="font-medium">
                    Document Type
                  </Label>
                  <Select
                    value={form.type}
                    onValueChange={value => updateFormField('type', value as DocumentType)}
                    disabled={isSubmitting}
                  >
                    <SelectTrigger className="transition-colors">
                      <SelectValue placeholder="Select document type" />
                    </SelectTrigger>
                    <SelectContent>
                      {DOCUMENT_TYPES.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label className="font-medium">
                    Project
                  </Label>
                  <div className="relative">
                    <Input
                      value={currentProject?.title || 'Loading project...'}
                      disabled={true}
                      className="bg-muted/50 border-dashed"
                      placeholder="Project will be auto-filled"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground flex items-center">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Project is automatically set based on the document and cannot be changed.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <DocumentEditor
                content={form.content}
                onUpdate={content => updateFormField('content', content)}
                onWordCountChange={setWordCount}
                editable={!isSubmitting}
              />
              <div className="mt-2 text-sm text-muted-foreground text-right">
                {wordCount} words
              </div>
            </CardContent>
          </Card>

          <div className="flex flex-col sm:flex-row justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push(`/documents/${document.id}`)}
              disabled={isSubmitting}
              className="min-w-[120px] transition-colors"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="submit"
              variant="default"
              disabled={isSubmitting || !form.title.trim()}
              title={!form.title.trim() ? "Please provide a title to save as draft" : "Save document as draft and continue editing"}
              className={`min-w-[140px] transition-all ${hasUnsavedChanges ? "ring-2 ring-amber-500/50 shadow-lg" : ""}`}
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {hasUnsavedChanges ? "Save Changes" : "Save as Draft"}
                </>
              )}
            </Button>
          </div>
        </form>
      </main>
    </div>
  );
}
