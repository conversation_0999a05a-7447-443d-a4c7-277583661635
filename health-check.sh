#!/bin/bash

# ==============================================
# Health Check Script for TMS Docker Deployment
# ==============================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# Function to check if a service is running
check_service() {
    local service_name=$1
    local container_name=$2
    
    if docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        print_success "$service_name is running"
        return 0
    else
        print_error "$service_name is not running"
        return 1
    fi
}

# Function to check if a port is accessible
check_port() {
    local service_name=$1
    local host=$2
    local port=$3
    
    if curl -s --connect-timeout 5 "$host:$port" > /dev/null 2>&1; then
        print_success "$service_name is accessible on port $port"
        return 0
    else
        print_error "$service_name is not accessible on port $port"
        return 1
    fi
}

# Function to check HTTP endpoint
check_http_endpoint() {
    local service_name=$1
    local url=$2
    local expected_status=${3:-200}
    
    local status_code=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "$url" 2>/dev/null || echo "000")
    
    if [ "$status_code" = "$expected_status" ]; then
        print_success "$service_name HTTP endpoint is healthy (Status: $status_code)"
        return 0
    else
        print_error "$service_name HTTP endpoint is unhealthy (Status: $status_code)"
        return 1
    fi
}

# Function to check database connection
check_database() {
    local db_container="appwrite-mariadb"
    
    if docker exec "$db_container" mysqladmin ping -h localhost --silent > /dev/null 2>&1; then
        print_success "Database is responding"
        return 0
    else
        print_error "Database is not responding"
        return 1
    fi
}

# Function to check Redis
check_redis() {
    local redis_container="appwrite-redis"
    
    if docker exec "$redis_container" redis-cli ping | grep -q "PONG"; then
        print_success "Redis is responding"
        return 0
    else
        print_error "Redis is not responding"
        return 1
    fi
}

# Function to check disk space
check_disk_space() {
    local threshold=80
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$usage" -lt "$threshold" ]; then
        print_success "Disk space usage is healthy ($usage%)"
        return 0
    else
        print_warning "Disk space usage is high ($usage%)"
        return 1
    fi
}

# Function to check memory usage
check_memory() {
    local threshold=80
    local usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ "$usage" -lt "$threshold" ]; then
        print_success "Memory usage is healthy ($usage%)"
        return 0
    else
        print_warning "Memory usage is high ($usage%)"
        return 1
    fi
}

# Function to check Docker volumes
check_volumes() {
    local volumes=("tms_appwrite-mariadb" "tms_appwrite-redis" "tms_appwrite-uploads")
    local all_good=true
    
    for volume in "${volumes[@]}"; do
        if docker volume inspect "$volume" > /dev/null 2>&1; then
            print_success "Volume $volume exists"
        else
            print_error "Volume $volume is missing"
            all_good=false
        fi
    done
    
    return $all_good
}

# Main health check function
run_health_check() {
    print_status "Starting health check for TMS Docker deployment..."
    echo ""
    
    local overall_status=0
    
    # Check system resources
    print_status "Checking system resources..."
    check_disk_space || overall_status=1
    check_memory || overall_status=1
    echo ""
    
    # Check Docker volumes
    print_status "Checking Docker volumes..."
    check_volumes || overall_status=1
    echo ""
    
    # Check if services are running
    print_status "Checking service containers..."
    check_service "Next.js App" "tms-app" || overall_status=1
    check_service "Appwrite" "appwrite" || overall_status=1
    check_service "MariaDB" "appwrite-mariadb" || overall_status=1
    check_service "Redis" "appwrite-redis" || overall_status=1
    check_service "InfluxDB" "appwrite-influxdb" || overall_status=1
    
    # Check if Nginx is running (production only)
    if docker ps --format "table {{.Names}}" | grep -q "tms-nginx"; then
        check_service "Nginx" "tms-nginx" || overall_status=1
    fi
    echo ""
    
    # Check database and cache connectivity
    print_status "Checking database and cache connectivity..."
    check_database || overall_status=1
    check_redis || overall_status=1
    echo ""
    
    # Check HTTP endpoints
    print_status "Checking HTTP endpoints..."
    
    # Check if running in development or production mode
    if docker ps --format "table {{.Names}}" | grep -q "tms-nginx"; then
        # Production mode with Nginx
        check_http_endpoint "Frontend (via Nginx)" "http://localhost" || overall_status=1
        check_http_endpoint "Appwrite API (via Nginx)" "http://localhost/v1/health" || overall_status=1
    else
        # Development mode
        check_http_endpoint "Frontend (Direct)" "http://localhost:3000" || overall_status=1
        check_http_endpoint "Appwrite API (Direct)" "http://localhost:8080/v1/health" || overall_status=1
    fi
    echo ""
    
    # Overall status
    if [ $overall_status -eq 0 ]; then
        print_success "All health checks passed! 🎉"
        echo ""
        print_status "Service URLs:"
        if docker ps --format "table {{.Names}}" | grep -q "tms-nginx"; then
            print_status "  Frontend: http://localhost"
            print_status "  Appwrite Console: http://localhost/console"
        else
            print_status "  Frontend: http://localhost:3000"
            print_status "  Appwrite Console: http://localhost:8080/console"
        fi
    else
        print_error "Some health checks failed! Please check the issues above."
        echo ""
        print_status "Troubleshooting tips:"
        print_status "  - Check service logs: ./deploy.sh logs [service-name]"
        print_status "  - Check service status: ./deploy.sh status"
        print_status "  - Restart services: docker-compose restart"
    fi
    
    return $overall_status
}

# Function to show detailed service information
show_service_info() {
    print_status "Detailed service information:"
    echo ""
    
    print_status "Container Status:"
    docker-compose ps
    echo ""
    
    print_status "Resource Usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}"
    echo ""
    
    print_status "Volume Usage:"
    docker system df -v | grep -E "(VOLUME NAME|tms_)"
}

# Main script logic
case "$1" in
    "check"|"")
        run_health_check
        ;;
    "info")
        show_service_info
        ;;
    "quick")
        print_status "Quick health check..."
        docker-compose ps
        ;;
    *)
        echo "Usage: $0 {check|info|quick}"
        echo ""
        echo "Commands:"
        echo "  check  - Run full health check (default)"
        echo "  info   - Show detailed service information"
        echo "  quick  - Quick status check"
        exit 1
        ;;
esac
