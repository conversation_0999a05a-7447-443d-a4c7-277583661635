import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getUsersByRole } from '@/lib/api/users';
import { updateProject } from '@/lib/api/projects';
import { User, UserRole } from '@/lib/types';
import { useToast } from '@/hooks/useToast';

interface TeamManagementProps {
  projectId: string;
  currentTeam: User[];
  onTeamUpdate: (newTeam: User[]) => void;
}

export function TeamManagement({ projectId, currentTeam, onTeamUpdate }: TeamManagementProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UserRole>('supervisor');
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { data: availableUsers } = useQuery({
    queryKey: ['users', selectedRole],
    queryFn: () => getUsersByRole(selectedRole)
  });

  const updateTeamMutation = useMutation({
    mutationFn: (newTeam: string[]) => 
      updateProject(projectId, { teamMembers: newTeam }),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      toast({
        title: 'Team updated',
        description: `The project team has been updated with ${variables.length} members.`,
      });
      setIsDialogOpen(false);
    },
    onError: (error) => {
      toast({
        variant: "destructive",
        title: "Failed to update team",
        description: "There was an error updating the team. Please try again.",
      });
      console.error('Team update error:', error);
    }
  });

  const handleAddMember = (userId: string) => {
    const newTeam = [...currentTeam, availableUsers!.find(u => u.id === userId)!];
    updateTeamMutation.mutate(newTeam.map(u => u.id));
    onTeamUpdate(newTeam);
  };

  const handleRemoveMember = async (memberId: string) => {
    try {
      const newTeam = currentTeam.filter(user => user.id !== memberId);
      await updateTeamMutation.mutateAsync(newTeam.map(user => user.id));
      toast({
        title: "Member removed",
        description: "Team member has been removed from the project.",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to remove team member. Please try again.",
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Team Members</h3>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline">Add Member</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Team Member</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <Select value={selectedRole} onValueChange={(value: UserRole) => setSelectedRole(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="supervisor">Supervisor</SelectItem>
                  <SelectItem value="student">Student</SelectItem>
                </SelectContent>
              </Select>
              <div className="space-y-2">
                {availableUsers?.map(user => (
                  <div key={user.id} className="flex items-center justify-between p-2 border rounded">
                    <div className="flex items-center gap-2">
                      <Avatar>
                        <AvatarImage src={user.profileImage} />
                        <AvatarFallback>{user.name[0]}</AvatarFallback>
                      </Avatar>
                      <span>{user.name}</span>
                    </div>
                    <Button
                      variant="ghost"
                      onClick={() => handleAddMember(user.id)}
                      disabled={currentTeam.some(t => t.id === user.id)}
                    >
                      Add
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="space-y-2">
        {currentTeam.map(member => (
          <div key={member.id} className="flex items-center justify-between p-2 border rounded">
            <div className="flex items-center gap-2">
              <Avatar>
                <AvatarImage src={member.profileImage} />
                <AvatarFallback>{member.name[0]}</AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">{member.name}</p>
                <p className="text-sm text-muted-foreground">{member.role}</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleRemoveMember(member.id)}
            >
              Remove
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
}





