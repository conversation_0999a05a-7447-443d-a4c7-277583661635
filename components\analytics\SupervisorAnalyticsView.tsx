"use client";

import { useMemo } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useAnalytics } from "@/hooks/useAnalytics";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Legend,
} from "recharts";
import {
  Users,
  FileText,
  Clock,
  CheckCircle2,
  AlertTriangle,
  TrendingUp,
  Calendar,
  Target,
  BookOpen,
} from "lucide-react";
import { format } from "date-fns";

interface SupervisorAnalyticsViewProps {
  timeRange?: "month" | "quarter" | "year";
}

const STATUS_COLORS = {
  active: "#10b981",
  completed: "#3b82f6",
  pending_supervisor: "#f59e0b",
  under_review: "#8b5cf6",
  approved: "#10b981",
  rejected: "#ef4444",
  draft: "#6b7280",
};

export function SupervisorAnalyticsView({ timeRange = "quarter" }: SupervisorAnalyticsViewProps) {
  const { user } = useAuth();
  
  const {
    analytics,
    isLoading,
    error,
    refreshAnalytics,
    rawData
  } = useAnalytics({
    timeRange,
    enableRealTime: true,
    includeExtendedAnalytics: true,
    filters: { userRole: 'supervisor' }
  });

  // Calculate supervisor-specific metrics
  const supervisorMetrics = useMemo(() => {
    if (!analytics || !user) return null;

    const supervisedProjects = rawData.projects.filter(p => 
      p.supervisorIds && p.supervisorIds.includes(user.id)
    );
    
    const supervisedStudents = new Set(supervisedProjects.map(p => p.studentId)).size;
    const activeProjects = analytics.projectsByStatus.active || 0;
    const completedProjects = analytics.projectsByStatus.completed || 0;
    const pendingReviews = analytics.documentAnalytics.documentsByStatus.under_review || 0;
    const totalDocuments = analytics.documentAnalytics.totalDocuments;
    const approvedDocuments = analytics.documentAnalytics.documentsByStatus.approved || 0;
    const rejectedDocuments = analytics.documentAnalytics.documentsByStatus.rejected || 0;
    const overdueMilestones = analytics.milestoneAnalytics.overdueMilestones || 0;
    
    const approvalRate = totalDocuments > 0 ? (approvedDocuments / totalDocuments) * 100 : 0;
    const rejectionRate = totalDocuments > 0 ? (rejectedDocuments / totalDocuments) * 100 : 0;
    const avgProjectsPerStudent = supervisedStudents > 0 ? supervisedProjects.length / supervisedStudents : 0;

    return {
      supervisedProjects: supervisedProjects.length,
      supervisedStudents,
      activeProjects,
      completedProjects,
      pendingReviews,
      totalDocuments,
      approvedDocuments,
      rejectedDocuments,
      overdueMilestones,
      approvalRate,
      rejectionRate,
      avgProjectsPerStudent,
    };
  }, [analytics, user, rawData.projects]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-8 bg-muted rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error || !analytics || !supervisorMetrics) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>Unable to load supervision analytics</p>
            <Button variant="outline" onClick={refreshAnalytics} className="mt-2">
              Try again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const projectStatusData = Object.entries(analytics.projectsByStatus).map(([status, count]) => ({
    name: status.replace('_', ' ').toUpperCase(),
    value: count,
    color: STATUS_COLORS[status as keyof typeof STATUS_COLORS] || "#6b7280"
  }));

  const documentReviewData = [
    { name: "Approved", value: supervisorMetrics.approvedDocuments, color: STATUS_COLORS.approved },
    { name: "Pending Review", value: supervisorMetrics.pendingReviews, color: STATUS_COLORS.under_review },
    { name: "Rejected", value: supervisorMetrics.rejectedDocuments, color: STATUS_COLORS.rejected },
  ].filter(item => item.value > 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Supervision Analytics</h2>
          <p className="text-muted-foreground">
            Overview of your supervised projects and student progress
          </p>
        </div>
        <Badge variant="outline" className="text-sm">
          {timeRange.charAt(0).toUpperCase() + timeRange.slice(1)} View
        </Badge>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Supervised Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{supervisorMetrics.supervisedStudents}</div>
            <p className="text-xs text-muted-foreground">
              {supervisorMetrics.supervisedProjects} total projects
            </p>
          </CardContent>
        </Card>

        <Card className={supervisorMetrics.pendingReviews > 0 ? "border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/20" : ""}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className={`text-sm font-medium ${supervisorMetrics.pendingReviews > 0 ? "text-amber-700 dark:text-amber-300" : ""}`}>
              Review Queue
            </CardTitle>
            <FileText className={`h-4 w-4 ${supervisorMetrics.pendingReviews > 0 ? "text-amber-500" : "text-muted-foreground"}`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${supervisorMetrics.pendingReviews > 0 ? "text-amber-600 dark:text-amber-400" : ""}`}>
              {supervisorMetrics.pendingReviews}
            </div>
            <p className={`text-xs ${supervisorMetrics.pendingReviews > 0 ? "text-amber-600 dark:text-amber-400" : "text-muted-foreground"}`}>
              documents awaiting review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{supervisorMetrics.activeProjects}</div>
            <p className="text-xs text-muted-foreground">
              {supervisorMetrics.completedProjects} completed
            </p>
          </CardContent>
        </Card>

        <Card className={supervisorMetrics.overdueMilestones > 0 ? "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20" : ""}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className={`text-sm font-medium ${supervisorMetrics.overdueMilestones > 0 ? "text-red-700 dark:text-red-300" : ""}`}>
              {supervisorMetrics.overdueMilestones > 0 ? "Overdue Milestones" : "On Schedule"}
            </CardTitle>
            {supervisorMetrics.overdueMilestones > 0 ? (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            ) : (
              <CheckCircle2 className="h-4 w-4 text-green-500" />
            )}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${supervisorMetrics.overdueMilestones > 0 ? "text-red-600 dark:text-red-400" : "text-green-600 dark:text-green-400"}`}>
              {supervisorMetrics.overdueMilestones > 0 ? supervisorMetrics.overdueMilestones : "✓"}
            </div>
            <p className={`text-xs ${supervisorMetrics.overdueMilestones > 0 ? "text-red-600 dark:text-red-400" : "text-muted-foreground"}`}>
              {supervisorMetrics.overdueMilestones > 0 ? "need attention" : "All on track"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Review Performance</CardTitle>
            <CardDescription>Document approval and rejection rates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Approval Rate</span>
                <span className="text-2xl font-bold text-green-600">
                  {supervisorMetrics.approvalRate.toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Rejection Rate</span>
                <span className="text-2xl font-bold text-red-600">
                  {supervisorMetrics.rejectionRate.toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Total Reviewed</span>
                <span className="text-lg font-semibold">
                  {supervisorMetrics.totalDocuments}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Project Status Distribution</CardTitle>
            <CardDescription>Current status of supervised projects</CardDescription>
          </CardHeader>
          <CardContent>
            {projectStatusData.length > 0 ? (
              <div className="h-[200px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={projectStatusData}
                      cx="50%"
                      cy="50%"
                      outerRadius={60}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {projectStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <BookOpen className="h-8 w-8 mx-auto mb-2" />
                <p>No projects assigned yet</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Document Review Status</CardTitle>
            <CardDescription>Breakdown of document review outcomes</CardDescription>
          </CardHeader>
          <CardContent>
            {documentReviewData.length > 0 ? (
              <div className="h-[200px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={documentReviewData}
                      cx="50%"
                      cy="50%"
                      outerRadius={60}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {documentReviewData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-8 w-8 mx-auto mb-2" />
                <p>No documents to review</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Workload Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Supervision Workload</CardTitle>
          <CardDescription>Analysis of your supervision responsibilities</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-primary">
                {supervisorMetrics.avgProjectsPerStudent.toFixed(1)}
              </div>
              <div className="text-sm text-muted-foreground">
                Avg Projects per Student
              </div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-primary">
                {supervisorMetrics.totalDocuments > 0 ? 
                  (supervisorMetrics.totalDocuments / supervisorMetrics.supervisedStudents).toFixed(1) : 
                  "0"
                }
              </div>
              <div className="text-sm text-muted-foreground">
                Avg Documents per Student
              </div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-primary">
                {supervisorMetrics.pendingReviews > 0 ? 
                  Math.ceil(supervisorMetrics.pendingReviews / 7) : 
                  0
                }
              </div>
              <div className="text-sm text-muted-foreground">
                Est. Review Days Needed
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
