import { account, databases, APPWRITE_CONFIG, avatars } from "@/lib/api";
import { User, UserRole } from "@/lib/types";
import { ID, AppwriteException } from "appwrite";
import { mapUserDocument } from "../utils/appwrite";


// Login
export async function loginUser(
  email: string,
  password: string
): Promise<User> {
  try {
    await account.createEmailPasswordSession(email, password);

    const accountDetails = await account.get();

    // Update user status to online
    await databases.updateDocument(
      APPWRITE_CONFIG.databaseId,
      APPWRITE_CONFIG.collections.profiles,
      accountDetails.$id,
      {
        status: "online",
        lastActive: new Date().toISOString()
      }
    );

    const userDoc = await databases.getDocument(
      APPWRITE_CONFIG.databaseId,
      APPWRITE_CONFIG.collections.profiles,
      accountDetails.$id
    );

    return mapUserDocument(userDoc);
  } catch (error) {
    const message =
      error instanceof AppwriteException
        ? error.message || "Appwrite login error"
        : "Unexpected login error";
    throw new Error(message);
  }
}

// Signup
export async function signupUser(
  email: string,
  password: string,
  name: string,
  department?: string,
  specialization?: string,
  bio?: string,
  role?: UserRole,
  profileImage?: string,
  lastActive?: Date,
  status?: "online" | "offline" | "away",
): Promise<User> {
  try {
    const newAccount = await account.create(ID.unique(), email, password, name);

    if (!newAccount) throw Error;

    console.log("Account created:", newAccount);

    const userID = newAccount.$id;
    console.log("New User ID:", userID);

    // Default settings for new users
    const defaultSettings = {
      notifications: {
        enableDesktopNotifications: true,
        emailNotifications: true,
      },
      display: {
        compactView: false,
        highContrastMode: false,
        largeText: false,
      },
    };

    const userDoc = await databases.createDocument(
      APPWRITE_CONFIG.databaseId,
      APPWRITE_CONFIG.collections.profiles,
      userID,
      {
        role: role || "student",
        department: department?.trim() || "",
        specialization: specialization?.trim() || "",
        bio: bio?.trim() || "",
        profileImage: profileImage || avatars.getInitials(name),
        lastActive: lastActive?.toISOString() || new Date().toISOString(),
        status: status || "offline",
        settings: JSON.stringify(defaultSettings),
      }
    );

    console.log("User document created:", userDoc);

    return mapUserDocument(userDoc);
  } catch (error) {
    console.error("Signup error:", error);

    if (error instanceof AppwriteException) {
      if (error.message?.toLowerCase().includes("network")) {
        throw new Error(
          "Network connection failed. Please check your internet connection and try again."
        );
      }
      if (error.code === 409) {
        throw new Error("An account with this email already exists.");
      }
      throw new Error(error.message || "Appwrite signup error");
    }

    throw new Error("Unexpected signup error");
  }
}

// Get current user
export async function getCurrentUser(): Promise<User | null> {
  try {
    const session = await account.getSession("current");
    if (!session) return null;

    const accountDetails = await account.get();

    const userDoc = await databases.getDocument(
      APPWRITE_CONFIG.databaseId,
      APPWRITE_CONFIG.collections.profiles,
      accountDetails.$id
    );

    return mapUserDocument(userDoc);
  } catch (error) {
    if (
      error instanceof AppwriteException &&
      (error.type === "user_unauthorized" ||
        error.code === 401 ||
        error.message.includes("missing scope"))
    ) {
      return null;
    }

    console.error("Error fetching current user:", error);
    return null;
  }
}

// Update user
export async function updateUser(
  userId: string,
  updates: Partial<User>
): Promise<User> {
  try {
    const { name, email, id, role, ...validUpdates } = updates;

    // Only proceed with database update if there are valid updates
    if (Object.keys(validUpdates).length > 0) {
      // Process settings object if it exists
      const updateData: any = {
        ...validUpdates,
        // Ensure profileImage is included if it exists in updates
        ...(updates.profileImage && { profileImage: updates.profileImage }),
      };

      // Convert settings object to JSON string if it exists
      if (updates.settings) {
        updateData.settings = JSON.stringify(updates.settings);
      }

      await databases.updateDocument(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.profiles,
        userId,
        updateData
      );
    }

    if (name) {
      await account.updateName(name);
    }

    const user = await getCurrentUser();
    if (!user) throw new Error("Failed to fetch updated user data");

    return user;
  } catch (error) {
    console.error("Error updating user:", error);
    if (error instanceof AppwriteException) {
      throw new Error(
        error.message || "Failed to update user. Please try again."
      );
    }
    throw new Error("Failed to update user. Please try again.");
  }
}

// Logout
export async function logoutUser() {
  try {
    // Get current user before logging out to get the ID
    const currentUser = await getCurrentUser();
    if (currentUser?.id) {
      await databases.updateDocument(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.profiles,
        currentUser.id,
        {
          status: "offline",
        }
      );
    }
    await account.deleteSessions();
  } catch (error) {
    console.error("Error logging out:", error);
    throw new Error("Logout failed. Please try again.");
  }
}

// Check auth status
export async function checkAuthStatus(): Promise<boolean> {
  try {
    const session = await account.getSession("current");
    return !!session;
  } catch (error) {
    // Specifically handle 401 errors as expected behavior
    if (
      error instanceof AppwriteException &&
      (error.code === 401 || error.type === "user_unauthorized")
    ) {
      return false;
    }
    // Log other unexpected errors
    console.error("Auth status check failed:", error);
    return false;
  }
}

// change password
export async function changePassword(currentPassword: string, newPassword: string) {
  try {
    await account.updatePassword(currentPassword, newPassword);
  } catch (error) {
    console.error("Error changing password:", error);
    if (error instanceof AppwriteException) {
      throw new Error(
        error.message || "Failed to change password. Please try again."
      );
    }
    throw new Error("Failed to change password. Please try again.");
  }
}


// request password reset
export async function requestPasswordReset(email: string) {
  try {
    await account.createRecovery(email, `${window.location.origin}/auth/reset-password`);
  } catch (error) {
    console.error("Error requesting password reset:", error);
    if (error instanceof AppwriteException) {
      throw new Error(
        error.message || "Failed to request password reset. Please try again."
      );
    }
    throw new Error("Failed to request password reset. Please try again.");
  }
}

// complete password reset
export async function completePasswordReset(
  userId: string,
  secret: string,
  password: string,
) {
  try {
    await account.updateRecovery(userId, secret, password);
  } catch (error) {
    console.error("Error resetting password:", error);
    if (error instanceof AppwriteException) {
      throw new Error(
        error.message || "Failed to reset password. Please try again."
      );
    }
    throw new Error("Failed to reset password. Please try again.");
  }
}
