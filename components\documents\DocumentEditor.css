.ProseMirror {
  min-height: 200px; /* Ensure there is enough height */
  caret-color: auto; /* Ensure cursor color is set */
}

.ProseMirror[contenteditable="true"] {
  outline: none; /* Prevent browsers from removing the cursor due to focus outline */
}

.ProseMirror h1 {
  font-size: 2.25rem;
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
  line-height: 1.2;
}
.ProseMirror h2 {
  font-size: 1.875rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}
.ProseMirror h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}
.ProseMirror p {
  margin-bottom: 1rem;
}
.ProseMirror blockquote {
  border-left: 4px solid currentColor;
  margin-left: 0;
  margin-right: 0;
  padding-left: 1rem;
  font-style: italic;
}
.ProseMirror ul,
.ProseMirror ol {
  margin: 0 0 1rem 0;
  padding: 0;
  list-style-position: inside;
  width: 100%;
}
.ProseMirror ul {
  list-style: none;
}
.ProseMirror ol {
  list-style: none;
  counter-reset: item;
}
.ProseMirror li {
  margin-bottom: 0.25rem;
  display: flex;
  align-items: flex-start;
}
.ProseMirror ul > li::before {
  content: "•";
  display: inline-block;
  width: 1.5rem;
  margin-right: 0.5rem;
  text-align: center;
  flex-shrink: 0;
}
.ProseMirror ol > li {
  counter-increment: item;
}
.ProseMirror ol > li::before {
  content: counter(item) ".";
  display: inline-block;
  width: 1.5rem;
  margin-right: 0.5rem;
  text-align: right;
  flex-shrink: 0;
}

/* ✅ Improved List Alignment Styles */
.ProseMirror ul[data-text-align="center"],
.ProseMirror ol[data-text-align="center"] {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
  display: block;
}

.ProseMirror ul[data-text-align="right"],
.ProseMirror ol[data-text-align="right"] {
  text-align: right;
  margin-left: auto;
  display: block;
}

.ProseMirror ul[data-text-align="left"],
.ProseMirror ol[data-text-align="left"] {
  text-align: left;
  margin-right: auto;
  display: block;
}

.ProseMirror li > p {
  margin: 0;
  display: inline;
}
.quote-block::before {
  content: '"';
}
.quote-block::after {
  content: '"';
}
