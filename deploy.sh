#!/bin/bash

# ==============================================
# Thesis Management System - Deployment Script
# ==============================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if required commands exist
check_requirements() {
    print_status "Checking requirements..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "All requirements met."
}

# Function to check if .env file exists
check_env_file() {
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from template..."
        cp .env.docker .env
        print_warning "Please edit .env file with your configuration before continuing."
        print_warning "Required variables: APPWRITE_PROJECT_ID, APPWRITE_DATABASE_ID, _APP_OPENSSL_KEY_V1, _APP_EXECUTOR_SECRET"
        exit 1
    fi
    print_success ".env file found."
}

# Function to generate secure keys
generate_keys() {
    print_status "Checking for secure keys in .env file..."
    
    if grep -q "your-openssl-key-here" .env; then
        print_warning "Generating secure OpenSSL key..."
        OPENSSL_KEY=$(openssl rand -base64 32)
        sed -i "s/your-openssl-key-here/$OPENSSL_KEY/" .env
    fi
    
    if grep -q "your-executor-secret-here" .env; then
        print_warning "Generating secure executor secret..."
        EXECUTOR_SECRET=$(openssl rand -base64 32)
        sed -i "s/your-executor-secret-here/$EXECUTOR_SECRET/" .env
    fi
    
    print_success "Security keys configured."
}

# Function to deploy for development
deploy_development() {
    print_status "Deploying for development..."
    
    # Build and start services
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build -d
    
    print_success "Development deployment completed!"
    print_status "Application will be available at:"
    print_status "  - Frontend: http://localhost:3000"
    print_status "  - Appwrite Console: http://localhost:8080/console"
}

# Function to deploy for production
deploy_production() {
    print_status "Deploying for production..."
    
    # Build and start services
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --build -d
    
    print_success "Production deployment completed!"
    print_status "Application will be available at:"
    print_status "  - Frontend: http://localhost (via Nginx)"
    print_status "  - Appwrite Console: http://localhost/console"
}

# Function to setup SSL certificates
setup_ssl() {
    print_status "Setting up SSL certificates..."
    
    if [ -z "$1" ]; then
        print_error "Domain name required for SSL setup."
        print_status "Usage: $0 ssl <domain>"
        exit 1
    fi
    
    DOMAIN=$1
    
    # Update domain in .env
    sed -i "s/DOMAIN=localhost/DOMAIN=$DOMAIN/" .env
    
    # Create SSL directories
    mkdir -p ssl ssl-challenge
    
    # Run certbot to get certificates
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml --profile ssl-setup run --rm certbot
    
    print_success "SSL certificates obtained for $DOMAIN"
    print_warning "Please update nginx.conf to enable HTTPS configuration."
}

# Function to show logs
show_logs() {
    SERVICE=${1:-}
    if [ -z "$SERVICE" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f "$SERVICE"
    fi
}

# Function to stop services
stop_services() {
    print_status "Stopping services..."
    docker-compose down
    print_success "Services stopped."
}

# Function to clean up
cleanup() {
    print_status "Cleaning up..."
    docker-compose down -v --remove-orphans
    docker system prune -f
    print_success "Cleanup completed."
}

# Function to show status
show_status() {
    print_status "Service status:"
    docker-compose ps
}

# Function to backup data
backup_data() {
    print_status "Creating backup..."
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup database
    docker-compose exec mariadb mysqldump -u root -p"${_APP_DB_ROOT_PASS}" appwrite > "$BACKUP_DIR/database.sql"
    
    # Backup volumes
    docker run --rm -v tms_appwrite-uploads:/data -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/uploads.tar.gz -C /data .
    docker run --rm -v tms_appwrite-certificates:/data -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/certificates.tar.gz -C /data .
    
    print_success "Backup created in $BACKUP_DIR"
}

# Main script logic
case "$1" in
    "dev"|"development")
        check_requirements
        check_env_file
        generate_keys
        deploy_development
        ;;
    "prod"|"production")
        check_requirements
        check_env_file
        generate_keys
        deploy_production
        ;;
    "ssl")
        setup_ssl "$2"
        ;;
    "logs")
        show_logs "$2"
        ;;
    "stop")
        stop_services
        ;;
    "status")
        show_status
        ;;
    "cleanup")
        cleanup
        ;;
    "backup")
        backup_data
        ;;
    *)
        echo "Usage: $0 {dev|prod|ssl <domain>|logs [service]|stop|status|cleanup|backup}"
        echo ""
        echo "Commands:"
        echo "  dev        - Deploy for development"
        echo "  prod       - Deploy for production"
        echo "  ssl        - Setup SSL certificates for domain"
        echo "  logs       - Show logs (optionally for specific service)"
        echo "  stop       - Stop all services"
        echo "  status     - Show service status"
        echo "  cleanup    - Stop services and clean up"
        echo "  backup     - Create backup of data"
        exit 1
        ;;
esac
