"use client";

import { useState, useRef, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { Document, Comment, User, DocumentStatus } from "@/lib/types";
import { Button } from "../ui/button";
import { DocumentEditor } from "./DocumentEditor";
import { Textarea } from "../ui/textarea";
import { Input } from "../ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "../ui/dialog";
import "./DocumentEditor.css";
import { useAuth } from "@/hooks/useAuth";
import { Avatar } from "../ui/avatar";
import { useComment } from "@/hooks/useComment";
import { useUser } from "@/hooks/useUser";
import { useDocument } from "@/hooks/useDocument";
import { useProject } from "@/hooks/useProject";
import { Skeleton } from "../ui/skeleton";
import {
  AlertCircle,
  MessageSquare,
  Calendar,
  FileText,
  Search,
  Filter,
  CheckCircle2,
  XCircle
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import { Badge } from "../ui/badge";
import { PDFViewer } from "./PdfViewer";
import { getFileTypeFromUrl } from "@/lib/utils/file-utils";

// Extended Comment type to include additional properties from API
interface ExtendedComment extends Comment {
  createdAt?: string | Date;
}

// Comment filter types
type CommentFilterType = 'all' | 'resolved' | 'unresolved';

// Status color mapping for document status badges
const STATUS_COLORS: Record<DocumentStatus, string> = {
  approved: "bg-green-500/10 text-green-500 border-green-500/20",
  rejected: "bg-red-500/10 text-red-500 border-red-500/20",
  under_review: "bg-yellow-500/10 text-yellow-500 border-yellow-500/20",
  draft: "bg-gray-500/10 text-gray-500 border-gray-500/20",
};

interface DocumentViewerProps {
  document: Document;
}

export function DocumentViewer({ document }: DocumentViewerProps) {
  const { user } = useAuth();
  const commentMutations = useComment();
  const { getDocumentComments } = useComment();
  const { useUsersByIdsQuery } = useUser();
  const { getById } = useDocument();
  const { useProjectQuery } = useProject();

  // Fetch the document using the useDocument hook to ensure proper data access
  // Only fetch if we need to refresh (e.g., for real-time updates)
  const {
    data: documentData,
    isLoading: isLoadingDocument,
    error: documentError
  } = getById(document.id);

  // Fetch project data to get team members
  const {
    data: projectData,
    isLoading: isLoadingProject,
    error: projectError
  } = useProjectQuery(document.projectId);

  // Use the fetched document or fall back to the prop
  const currentDocument = documentData || document;

  // State management with descriptive names
  const [selectedText, setSelectedText] = useState("");
  const [showCommentInput, setShowCommentInput] = useState(false);
  const [commentContent, setCommentContent] = useState("");
  const [selectionRange, setSelectionRange] = useState<{
    start: number;
    end: number;
  } | null>(null);
  const [selectedPage, setSelectedPage] = useState<number | null>(null);
  const [wordCount, setWordCount] = useState(0);
  const [commentError, setCommentError] = useState<string | null>(null);
  const [highlightColor, setHighlightColor] = useState<string>("yellow");

  // Comment filtering and search state
  const [commentFilter, setCommentFilter] = useState<CommentFilterType>('all');
  const [searchQuery, setSearchQuery] = useState("");

  // Check if the document has a PDF file attached
  const isPdfDocument = useMemo(() => {
    return currentDocument?.fileUrl && getFileTypeFromUrl(currentDocument.fileUrl) === 'pdf';
  }, [currentDocument?.fileUrl]);



  // Determine if we should show the PDF viewer
  const showPdfViewer = useMemo(() => {
    return isPdfDocument && !!currentDocument.fileUrl;
  }, [isPdfDocument, currentDocument.fileUrl]);

  // Ensure fileUrl is a string (not undefined)
  const pdfFileUrl = useMemo(() => {
    return currentDocument.fileUrl || '';
  }, [currentDocument.fileUrl]);

  // Use a more descriptive name for the timeout ref
  const selectionDebounceRef = useRef<NodeJS.Timeout | null>(null);

  // Get comments for this document with proper error handling
  const {
    data: comments = [],
    isLoading: isLoadingComments,
    error: commentsError
  } = getDocumentComments(currentDocument.id);

  // Extract unique user IDs from comments - memoized for performance
  const userIds = useMemo(() => {
    const ids = Array.from(new Set(comments.map((comment: Comment) => comment.userId)));
    return ids.filter(Boolean) as string[]; // Filter out any null/undefined and cast to string[]
  }, [comments]);

  // Fetch users involved in comments using the hook
  const {
    data: users = [],
    isLoading: isLoadingUsers,
    error: usersError
  } = useUsersByIdsQuery(userIds);

  // Convert users array to a lookup object for easier access - memoized
  const usersMap = useMemo(() =>
    users.reduce((acc: Record<string, User>, user) => {
      acc[user.id] = user;
      return acc;
    }, {}),
    [users]
  );

  // Filter comments based on filter type and search query
  const filteredComments = useMemo(() => {
    return comments.filter((comment) => {
      // Apply filter type
      if (commentFilter === 'resolved' && !comment.resolved) return false;
      if (commentFilter === 'unresolved' && comment.resolved) return false;

      // Apply search query if present
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        const content = comment.content.toLowerCase();
        const highlightText = comment.highlight?.text.toLowerCase() || '';
        const userName = usersMap[comment.userId]?.name.toLowerCase() || '';

        return (
          content.includes(query) ||
          highlightText.includes(query) ||
          userName.includes(query)
        );
      }

      return true;
    });
  }, [comments, commentFilter, searchQuery, usersMap]);

  // Cleanup timeout on unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      if (selectionDebounceRef.current) {
        clearTimeout(selectionDebounceRef.current);
      }
    };
  }, []);

  // Check if the current user can create new comments (team member supervisors for documents under review)
  const canCreateComments = useMemo(() => {
    if (!user || !currentDocument || !projectData) return false;

    // Only for documents that are under review
    if (currentDocument.status !== 'under_review') return false;

    // Only supervisors who are team members can create new comments
    const isTeamMember = projectData.teamMembers?.includes(user.id);
    const isSupervisor = user.role === 'supervisor';

    return isTeamMember && isSupervisor;
  }, [user, currentDocument, projectData]);

  // Check if the current user can reply to comments (team members or admins)
  const canReplyToComments = useMemo(() => {
    if (!user || !currentDocument || !projectData) return false;

    // Team members can reply to comments
    const isTeamMember = projectData.teamMembers?.includes(user.id);

    // Admins can reply
    const isAdmin = user.role === 'admin';

    return isTeamMember || isAdmin;
  }, [user, currentDocument, projectData]);

  // Improved text selection handler with better debouncing and role-based restrictions
  const handleTextSelection = useCallback(
    (text: string, range: { start: number; end: number }) => {
      // Early return for invalid selections
      if (!text?.trim() || !range || !user) return;

      // Role-based restriction: Only supervisors can select text for documents under review
      if (!canCreateComments) return;

      // Clear any existing timeout to prevent multiple dialogs
      if (selectionDebounceRef.current) {
        clearTimeout(selectionDebounceRef.current);
      }

      // Debounce the selection to avoid triggering on transient selections
      selectionDebounceRef.current = setTimeout(() => {
        // Double-check that selection still exists
        const selection = window.getSelection();
        if (!selection?.toString()) return;

        // Minimum selection length check
        if (text.trim().length < 2) return;

        setSelectedText(text);
        setSelectionRange(range);
        setShowCommentInput(true);
      }, 500);
    },
    [user, canCreateComments]
  );



  // Improved comment handling with better error management and optimistic updates
  const handleAddComment = useCallback(async () => {
    // Role-based validation: Only supervisors can add comments for documents under review
    if (!canCreateComments) {
      setCommentError("Only supervisors can add comments to documents under review.");
      return;
    }

    // Validate inputs with more specific error messages
    if (!commentContent.trim()) {
      setCommentError("Comment cannot be empty");
      return;
    }

    if (!selectionRange || !user) {
      setCommentError("Unable to add comment. Please try selecting text again.");
      return;
    }

    // Clear previous errors
    setCommentError(null);

    // Create the comment data
    const newComment = {
      documentId: currentDocument.id,
      userId: user.id,
      content: commentContent.trim(),
      highlight: {
        start: selectionRange.start,
        end: selectionRange.end,
        text: selectedText,
        page: selectedPage || undefined, // Include page number for PDF comments
        color: selectedPage ? highlightColor : undefined // Include highlight color for PDF comments
      },
      resolved: false,
      // Add a temporary ID for optimistic updates
      // This will be replaced by the server-generated ID
      id: `temp-${Date.now()}`
    };

    try {
      // Use type assertion to any to bypass the type checking
      // This is necessary because the hook expects Omit<Comment, "id">
      // but the API actually expects Omit<Comment, "id" | "createdAt">
      await commentMutations.create.mutateAsync(newComment as any, {
        // Add optimistic update options if needed
        onError: (error) => {
          console.error("Failed to add comment:", error);
          setCommentError(
            error instanceof Error
              ? `Failed to add comment: ${error.message}`
              : "Failed to add comment. Please try again."
          );
        }
      });

      // Reset state after successful comment
      setCommentContent("");
      setShowCommentInput(false);
      setSelectedText("");
      setSelectionRange(null);
      setSelectedPage(null);
    } catch (error) {
      console.error("Failed to add comment:", error);
      setCommentError(
        error instanceof Error
          ? `Failed to add comment: ${error.message}`
          : "Failed to add comment. Please try again."
      );
    }
  }, [canCreateComments, commentContent, selectionRange, user, currentDocument.id, selectedText, selectedPage, highlightColor, commentMutations.create]);

  // Memoized document metadata renderer for better performance
  const documentMetadata = useMemo(() => {
    if (!currentDocument) return null;

    return (
      <div className="flex flex-wrap gap-3 mb-4 text-sm">
        <div className="flex items-center gap-1.5">
          <FileText className="h-4 w-4 text-muted-foreground" />
          <span>Words: {wordCount}</span>
        </div>

        {currentDocument.updatedAt && (
          <div className="flex items-center gap-1.5">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span>Last updated: {new Date(currentDocument.updatedAt).toLocaleDateString()}</span>
          </div>
        )}



        <div className="flex items-center gap-1.5 ml-auto">
          <Badge className={STATUS_COLORS[currentDocument.status]}>
            {currentDocument.status.replace('_', ' ')}
          </Badge>
        </div>
      </div>
    );
  }, [currentDocument, wordCount]);

  // Handle loading states with better UX
  if (isLoadingDocument) {
    return <DocumentSkeleton />;
  }

  // Handle error states with more helpful messages
  if (documentError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error Loading Document</AlertTitle>
        <AlertDescription>
          {documentError instanceof Error
            ? documentError.message
            : "Failed to load document. Please try refreshing the page."}
        </AlertDescription>
        <Button
          variant="outline"
          className="mt-2"
          onClick={() => window.location.reload()}
        >
          Refresh Page
        </Button>
      </Alert>
    );
  }



  // Main render function
  return (
    <div className="space-y-6">
      <div className="border rounded-lg p-4 shadow-sm">
        <div className="flex justify-between items-center mb-2">
          <h1 className="text-2xl font-bold">{currentDocument.title}</h1>
        </div>
        {documentMetadata}

        {/* Render PDF viewer if document has a PDF file attached */}
        {showPdfViewer ? (
          <div className="mt-4">
            <div className="border rounded-md overflow-hidden">
              <PDFViewer
                fileUrl={pdfFileUrl}
                fileName={currentDocument.title}
                maxHeight={700}
              />
            </div>
          </div>
        ) : (
          <DocumentEditor
            content={currentDocument.content}
            editable={false}
            onWordCountChange={setWordCount}
            onTextSelection={canCreateComments ? (text, range) => handleTextSelection(text, range) : undefined}
          />
        )}
      </div>

      <CommentDialog
        isOpen={showCommentInput}
        onOpenChange={setShowCommentInput}
        selectedText={selectedText}
        commentContent={commentContent}
        setCommentContent={setCommentContent}
        onAddComment={handleAddComment}
        isPending={commentMutations.create.isPending}
        error={commentError}
        pageNumber={selectedPage}
        highlightColor={highlightColor}
        setHighlightColor={setHighlightColor}
      />

      <div className="mt-6">
        <div className="flex items-center gap-2 mb-4">
          <MessageSquare className="h-5 w-5 text-muted-foreground" />
          <h2 className="text-xl font-semibold">Comments</h2>
          {comments.length > 0 && (
            <Badge variant="outline" className="ml-2">
              {comments.length}
            </Badge>
          )}
        </div>

        {comments.length > 0 && (
          <div className="flex flex-col sm:flex-row gap-3 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search comments..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                aria-label="Search comments"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={commentFilter === 'all' ? "default" : "outline"}
                size="sm"
                onClick={() => setCommentFilter('all')}
                className="flex items-center gap-1.5"
              >
                <Filter className="h-4 w-4" />
                All
              </Button>
              <Button
                variant={commentFilter === 'resolved' ? "default" : "outline"}
                size="sm"
                onClick={() => setCommentFilter('resolved')}
                className="flex items-center gap-1.5"
              >
                <CheckCircle2 className="h-4 w-4" />
                Resolved
              </Button>
              <Button
                variant={commentFilter === 'unresolved' ? "default" : "outline"}
                size="sm"
                onClick={() => setCommentFilter('unresolved')}
                className="flex items-center gap-1.5"
              >
                <XCircle className="h-4 w-4" />
                Unresolved
              </Button>
            </div>
          </div>
        )}

        {isLoadingComments || isLoadingUsers ? (
          <CommentsSkeleton />
        ) : commentsError || usersError ? (
          <Alert variant="destructive" className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error Loading Comments</AlertTitle>
            <AlertDescription>
              {commentsError instanceof Error
                ? commentsError.message
                : "Failed to load comments. Please try refreshing the page."}
            </AlertDescription>
            <Button
              variant="outline"
              className="mt-2"
              onClick={() => window.location.reload()}
            >
              Refresh Comments
            </Button>
          </Alert>
        ) : (
          <CommentsList comments={filteredComments} usersMap={usersMap} />
        )}
      </div>
    </div>
  );
}

interface CommentDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedText: string;
  commentContent: string;
  setCommentContent: (content: string) => void;
  onAddComment: () => void;
  isPending: boolean;
  error: string | null;
  pageNumber?: number | null;
  highlightColor?: string;
  setHighlightColor?: (color: string) => void;
}

function CommentDialog({
  isOpen,
  onOpenChange,
  selectedText,
  commentContent,
  setCommentContent,
  onAddComment,
  isPending,
  error,
  pageNumber,
  highlightColor = 'yellow',
  setHighlightColor
}: CommentDialogProps) {
  // Handle keyboard shortcuts for the dialog
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Submit on Ctrl+Enter or Cmd+Enter
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      if (commentContent.trim() && !isPending) {
        onAddComment();
      }
      e.preventDefault();
    }
  };

  // Helper function to get highlight color with opacity
  const getHighlightColor = (color: string, opacity: number = 0.4): string => {
    const colorMap: Record<string, string> = {
      yellow: `rgba(255, 230, 0, ${opacity})`,
      green: `rgba(0, 230, 118, ${opacity})`,
      blue: `rgba(33, 150, 243, ${opacity})`,
      pink: `rgba(233, 30, 99, ${opacity})`,
      orange: `rgba(255, 152, 0, ${opacity})`
    };
    return colorMap[color] || colorMap.yellow;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add Comment</DialogTitle>
          <DialogDescription>
            Add your feedback or comment on the selected text.
            {pageNumber && <span className="ml-1">(Page {pageNumber})</span>}
          </DialogDescription>
        </DialogHeader>

        <div className="bg-muted/50 p-3 rounded text-sm my-2 border border-muted">
          <p className="font-medium text-xs text-muted-foreground mb-1">
            Selected text: {pageNumber && <span className="ml-1">Page {pageNumber}</span>}
          </p>
          <p className="text-sm break-words">&quot;{selectedText}&quot;</p>
        </div>

        {/* Highlight color selection */}
        {pageNumber && setHighlightColor && (
          <div className="mb-4">
            <label className="text-sm font-medium mb-2 block">Highlight Color:</label>
            <div className="flex gap-2">
              {['yellow', 'green', 'blue', 'pink', 'orange'].map((color) => (
                <button
                  key={color}
                  type="button"
                  className={`w-6 h-6 rounded-full border ${highlightColor === color ? 'ring-2 ring-primary ring-offset-2' : ''}`}
                  style={{ backgroundColor: getHighlightColor(color, 0.4) }}
                  onClick={() => setHighlightColor(color)}
                  aria-label={`${color} highlight`}
                />
              ))}
            </div>
            <div className="mt-2 p-2 border rounded">
              <p className="text-sm">
                <span
                  style={{
                    backgroundColor: getHighlightColor(highlightColor, 0.4),
                    padding: '0 4px',
                    borderRadius: '2px'
                  }}
                >
                  Preview of highlighted text
                </span>
              </p>
            </div>
          </div>
        )}

        <Textarea
          placeholder="Write your comment..."
          value={commentContent}
          onChange={(e) => setCommentContent(e.target.value)}
          onKeyDown={handleKeyDown}
          className="min-h-[120px] resize-none focus:ring-1 focus:ring-primary"
          aria-label="Comment text"
          autoFocus
        />

        {error && (
          <Alert variant="destructive" className="mt-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="text-xs text-muted-foreground mt-1">
          Tip: Press Ctrl+Enter to submit
        </div>

        <DialogFooter className="gap-2 sm:gap-0">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isPending}
          >
            Cancel
          </Button>
          <Button
            onClick={onAddComment}
            disabled={!commentContent.trim() || isPending}
            className="relative"
          >
            {isPending ? (
              <>
                <span className="opacity-0">Add Comment</span>
                <span className="absolute inset-0 flex items-center justify-center">
                  Adding...
                </span>
              </>
            ) : (
              "Add Comment"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

interface CommentsListProps {
  comments: (Comment | ExtendedComment)[];
  usersMap: Record<string, User>;
}

function CommentsList({ comments, usersMap }: CommentsListProps) {
  // Group comments by date for better organization
  const groupedComments = useMemo(() => {
    const groups: Record<string, (Comment | ExtendedComment)[]> = {};

    comments.forEach(comment => {
      // Use $createdAt from Appwrite or fallback to current date
      const commentDate = (comment as ExtendedComment).createdAt;
      const date = commentDate
        ? new Date(commentDate).toLocaleDateString()
        : 'Unknown date';

      if (!groups[date]) {
        groups[date] = [];
      }

      groups[date].push(comment);
    });

    return groups;
  }, [comments]);

  // If there are no comments, show a message with better UX
  if (comments.length === 0) {
    return (
      <div className="text-center p-8 border rounded-lg bg-muted/10 flex flex-col items-center justify-center">
        <MessageSquare className="h-12 w-12 text-muted-foreground/50 mb-3" />
        <p className="text-muted-foreground font-medium">No comments yet</p>
        <p className="text-sm text-muted-foreground/70 mt-1">
          Supervisors can add comments. Document owners and team members can reply.
        </p>
      </div>
    );
  }

  // Get dates in reverse chronological order
  const dates = Object.keys(groupedComments).sort((a, b) => {
    if (a === 'Unknown date') return 1;
    if (b === 'Unknown date') return -1;
    return new Date(b).getTime() - new Date(a).getTime();
  });

  return (
    <div className="space-y-6">
      {dates.map(date => (
        <div key={date} className="space-y-3">
          <div className="sticky top-0 bg-background/95 backdrop-blur-sm z-10 py-1">
            <h3 className="text-sm font-medium text-muted-foreground">{date}</h3>
            <div className="h-px bg-border mt-1"></div>
          </div>

          <div className="space-y-4">
            {groupedComments[date].map((comment) => {
              const commentUser = usersMap[comment.userId];
              const commentDate = (comment as ExtendedComment).createdAt;
              const formattedTime = commentDate
                ? new Date(commentDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                : '';

              return (
                <div key={comment.id} className="border rounded-lg p-4 space-y-3 hover:border-primary/20 transition-colors">
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      {commentUser?.profileImage ? (
                        <img
                          src={commentUser.profileImage}
                          alt={commentUser?.name || "User"}
                          className="rounded-full object-cover"
                        />
                      ) : (
                        <div className="rounded-full bg-primary/20 flex items-center justify-center text-xs text-primary h-full w-full">
                          {commentUser?.name?.charAt(0) || "U"}
                        </div>
                      )}
                    </Avatar>
                    <div>
                      <span className="font-medium block leading-tight">
                        {commentUser?.name || "Unknown User"}
                      </span>
                      {formattedTime && (
                        <span className="text-xs text-muted-foreground">
                          {formattedTime}
                        </span>
                      )}
                    </div>
                  </div>

                  {comment.highlight && (
                    <div className="bg-muted/50 p-3 rounded-md text-sm border border-muted/50">
                      <p className="text-muted-foreground break-words italic">
                        &quot;{comment.highlight.text}&quot;
                      </p>
                    </div>
                  )}

                  <p className="text-sm break-words leading-relaxed">{comment.content}</p>
                </div>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
}

// Enhanced skeleton loaders for better UX during loading states
function DocumentSkeleton() {
  return (
    <div className="space-y-6 animate-pulse">
      <div className="border rounded-lg p-4 shadow-sm">
        {/* Title skeleton */}
        <Skeleton className="h-8 w-2/3 mb-4" />

        {/* Metadata skeleton */}
        <div className="flex flex-wrap gap-3 mb-6">
          <Skeleton className="h-5 w-24" />
          <Skeleton className="h-5 w-40" />
          <Skeleton className="h-5 w-20 ml-auto" />
        </div>

        {/* Content skeleton - paragraphs of varying width */}
        <div className="space-y-4 mt-6">
          <Skeleton className="h-5 w-full" />
          <Skeleton className="h-5 w-[95%]" />
          <Skeleton className="h-5 w-[90%]" />
          <Skeleton className="h-5 w-full" />
          <Skeleton className="h-5 w-[85%]" />

          {/* Heading-like skeleton */}
          <Skeleton className="h-7 w-1/2 mt-8" />

          <Skeleton className="h-5 w-[92%]" />
          <Skeleton className="h-5 w-[88%]" />
          <Skeleton className="h-5 w-full" />
        </div>
      </div>
    </div>
  );
}

function CommentsSkeleton() {
  // Create an array with different lengths for more realistic loading
  const commentLengths = [
    { highlight: true, contentLines: 2 },
    { highlight: false, contentLines: 3 },
    { highlight: true, contentLines: 1 }
  ];

  return (
    <div className="space-y-6 animate-pulse">
      {/* Date header skeleton */}
      <div className="py-1">
        <Skeleton className="h-5 w-32 mb-1" />
        <div className="h-px bg-border"></div>
      </div>

      {/* Comment skeletons */}
      <div className="space-y-4">
        {commentLengths.map((config, i) => (
          <div key={i} className="border rounded-lg p-4 space-y-3">
            {/* User info skeleton */}
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="space-y-1">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-16" />
              </div>
            </div>

            {/* Highlight skeleton (conditional) */}
            {config.highlight && (
              <div className="bg-muted/50 p-3 rounded-md border border-muted/50">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-4/5 mt-1" />
              </div>
            )}

            {/* Comment content skeleton */}
            <div className="space-y-2">
              {Array(config.contentLines).fill(0).map((_, j) => (
                <Skeleton key={j} className={`h-4 w-${j === config.contentLines - 1 ? '3/4' : 'full'}`} />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
