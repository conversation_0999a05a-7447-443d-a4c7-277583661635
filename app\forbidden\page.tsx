'use client';

import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';

export default function ForbiddenPage() {
  const { user, logout } = useAuth();
  const router = useRouter();

  const handleBackToDashboard = () => {
    if (user?.role) {
      router.push(`/dashboard/${user.role}`);
    } else {
      router.push('/');
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4">
      <div className="bg-card p-8 rounded-lg shadow-lg max-w-md w-full text-center">
        <h1 className="text-3xl font-bold text-destructive mb-4">Access Denied</h1>
        <p className="mb-6 text-muted-foreground">
          You don&apos;t have permission to access this resource.
        </p>
        <div className="flex flex-col gap-2">
          <Button onClick={handleBackToDashboard} className="w-full">
            Back to Dashboard
          </Button>
          <Button variant="outline" onClick={() => logout()} className="w-full">
            Sign Out
          </Button>
        </div>
      </div>
    </div>
  );
}