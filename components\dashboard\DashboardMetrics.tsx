import { StatsCard } from "@/components/ui/stats-card";
import { Project, User } from "@/lib/types";
import {
  Activity,
  Users,
  FileText,
  Clock,
  AlertCircle,
  CheckCircle,
  Calendar,
  TrendingUp,
} from "lucide-react";
import { useMemo } from "react";

interface DashboardMetricsProps {
  projects: Project[];
  users: User[];
  loading?: boolean;
  variant?: "overview" | "student" | "supervisor" | "manager";
  currentUserId?: string;
}

export function DashboardMetrics({
  projects,
  users,
  loading = false,
  variant = "overview",
  currentUserId,
}: DashboardMetricsProps) {
  const metrics = useMemo(() => {
    const activeProjects = projects.filter((p) => p.status === "active");
    const completedProjects = projects.filter((p) => p.status === "completed");
    const overdueProjects = projects.filter((p) => {
      const deadline = p.deadline ? new Date(p.deadline) : null;
      return deadline && deadline < new Date() && p.status !== "completed";
    });
    const pendingProjects = projects.filter((p) => p.status === "pending_supervisor");

    // Role-specific calculations
    let roleSpecificProjects = projects;
    let roleSpecificUsers = users;

    if (variant === "student" && currentUserId) {
      roleSpecificProjects = projects.filter((p) => p.studentId === currentUserId);
    } else if (variant === "supervisor" && currentUserId) {
      roleSpecificProjects = projects.filter((p) =>
        p.supervisorIds?.includes(currentUserId)
      );
      roleSpecificUsers = users.filter((u) =>
        u.role === "student" &&
        roleSpecificProjects.some((p) => p.studentId === u.id)
      );
    }

    const totalDocuments = roleSpecificProjects.reduce(
      (acc, p) => acc + (p.documents?.length || 0),
      0
    );
    const pendingDocuments = roleSpecificProjects.reduce(
      (acc, p) =>
        acc +
        (p.documents?.filter((d) => 
          typeof d === 'object' && d.status === "pending"
        ).length || 0),
      0
    );

    return {
      totalProjects: roleSpecificProjects.length,
      activeProjects: roleSpecificProjects.filter((p) => p.status === "active").length,
      completedProjects: roleSpecificProjects.filter((p) => p.status === "completed").length,
      overdueProjects: roleSpecificProjects.filter((p) => {
        const deadline = p.deadline ? new Date(p.deadline) : null;
        return deadline && deadline < new Date() && p.status !== "completed";
      }).length,
      pendingProjects: roleSpecificProjects.filter((p) => p.status === "pending_supervisor").length,
      totalUsers: roleSpecificUsers.length,
      totalDocuments,
      pendingDocuments,
      completionRate: roleSpecificProjects.length > 0 
        ? Math.round((roleSpecificProjects.filter((p) => p.status === "completed").length / roleSpecificProjects.length) * 100)
        : 0,
    };
  }, [projects, users, variant, currentUserId]);

  const getMetricsForVariant = () => {
    switch (variant) {
      case "student":
        return [
          {
            title: "My Projects",
            value: metrics.totalProjects,
            icon: <Activity className="h-4 w-4 text-blue-600 dark:text-blue-400" />,
            description: `${metrics.activeProjects} active`,
          },
          {
            title: "Completed",
            value: metrics.completedProjects,
            icon: <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />,
            description: `${metrics.completionRate}% completion rate`,
            variant: "success" as const,
          },
          {
            title: "Documents",
            value: metrics.totalDocuments,
            icon: <FileText className="h-4 w-4 text-purple-600 dark:text-purple-400" />,
            description: `${metrics.pendingDocuments} pending review`,
          },
          {
            title: "Overdue",
            value: metrics.overdueProjects,
            icon: <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />,
            description: metrics.overdueProjects > 0 ? "Needs attention" : "All on track",
            variant: metrics.overdueProjects > 0 ? ("danger" as const) : ("success" as const),
          },
        ];

      case "supervisor":
        return [
          {
            title: "Supervised Projects",
            value: metrics.totalProjects,
            icon: <Activity className="h-4 w-4 text-blue-600 dark:text-blue-400" />,
            description: `${metrics.activeProjects} active`,
          },
          {
            title: "Students",
            value: metrics.totalUsers,
            icon: <Users className="h-4 w-4 text-green-600 dark:text-green-400" />,
            description: "Under supervision",
          },
          {
            title: "Review Queue",
            value: metrics.pendingDocuments,
            icon: <Clock className="h-4 w-4 text-orange-600 dark:text-orange-400" />,
            description: `${metrics.totalDocuments} total documents`,
            variant: metrics.pendingDocuments > 5 ? ("warning" as const) : ("default" as const),
          },
          {
            title: "Completion Rate",
            value: `${metrics.completionRate}%`,
            icon: <TrendingUp className="h-4 w-4 text-purple-600 dark:text-purple-400" />,
            description: "Project completion",
            variant: metrics.completionRate >= 80 ? ("success" as const) : ("default" as const),
          },
        ];

      case "manager":
        return [
          {
            title: "Total Projects",
            value: metrics.totalProjects,
            icon: <Activity className="h-4 w-4 text-blue-600 dark:text-blue-400" />,
            description: `${metrics.activeProjects} active`,
          },
          {
            title: "Total Users",
            value: metrics.totalUsers,
            icon: <Users className="h-4 w-4 text-green-600 dark:text-green-400" />,
            description: "System users",
          },
          {
            title: "Pending Assignment",
            value: metrics.pendingProjects,
            icon: <Calendar className="h-4 w-4 text-orange-600 dark:text-orange-400" />,
            description: "Need supervisors",
            variant: metrics.pendingProjects > 0 ? ("warning" as const) : ("success" as const),
          },
          {
            title: "System Health",
            value: metrics.overdueProjects === 0 ? "Good" : "Issues",
            icon: <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />,
            description: `${metrics.overdueProjects} overdue projects`,
            variant: metrics.overdueProjects === 0 ? ("success" as const) : ("danger" as const),
          },
        ];

      default: // overview
        return [
          {
            title: "Active Projects",
            value: metrics.activeProjects,
            icon: <Activity className="h-4 w-4 text-blue-600 dark:text-blue-400" />,
            description: `${((metrics.activeProjects / Math.max(metrics.totalProjects, 1)) * 100).toFixed(1)}% of total`,
          },
          {
            title: "Total Users",
            value: metrics.totalUsers,
            icon: <Users className="h-4 w-4 text-green-600 dark:text-green-400" />,
            description: "Registered users",
          },
          {
            title: "Documents",
            value: metrics.totalDocuments,
            icon: <FileText className="h-4 w-4 text-purple-600 dark:text-purple-400" />,
            description: `${metrics.pendingDocuments} pending`,
          },
          {
            title: "Overdue",
            value: metrics.overdueProjects,
            icon: <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />,
            description: metrics.overdueProjects > 0 ? "Need attention" : "All on track",
            variant: metrics.overdueProjects > 0 ? ("danger" as const) : ("success" as const),
          },
        ];
    }
  };

  const metricsToShow = getMetricsForVariant();

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {metricsToShow.map((metric, index) => (
        <StatsCard
          key={index}
          title={metric.title}
          value={metric.value}
          icon={metric.icon}
          description={metric.description}
          variant={metric.variant}
          loading={loading}
        />
      ))}
    </div>
  );
}
