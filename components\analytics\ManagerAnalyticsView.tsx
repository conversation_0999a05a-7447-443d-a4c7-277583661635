"use client";

import { useMemo } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useAnalytics } from "@/hooks/useAnalytics";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Legend,
  Area,
  AreaChart,
} from "recharts";
import {
  Users,
  FileText,
  BookOpen,
  TrendingUp,
  AlertTriangle,
  CheckCircle2,
  Calendar,
  Target,
  BarChart3,
  Download,
} from "lucide-react";
import { format } from "date-fns";

interface ManagerAnalyticsViewProps {
  timeRange?: "month" | "quarter" | "year";
}

const STATUS_COLORS = {
  active: "#10b981",
  completed: "#3b82f6",
  pending_supervisor: "#f59e0b",
  archived: "#6b7280",
  suspended: "#ef4444",
  under_review: "#8b5cf6",
  approved: "#10b981",
  rejected: "#ef4444",
  draft: "#6b7280",
};

export function ManagerAnalyticsView({ timeRange = "year" }: ManagerAnalyticsViewProps) {
  const { user } = useAuth();
  
  const {
    analytics,
    isLoading,
    error,
    refreshAnalytics,
    exportAnalytics,
    rawData
  } = useAnalytics({
    timeRange,
    enableRealTime: true,
    includeExtendedAnalytics: true,
    filters: { userRole: 'all' } // Managers see all data
  });

  // Calculate comprehensive manager metrics
  const managerMetrics = useMemo(() => {
    if (!analytics) return null;

    const totalUsers = analytics.userEngagement.totalUsers;
    const activeUsers = analytics.userEngagement.activeUsers;
    const totalProjects = analytics.totalProjects;
    const activeProjects = analytics.projectsByStatus.active || 0;
    const completedProjects = analytics.projectsByStatus.completed || 0;
    const pendingSupervisorProjects = analytics.projectsByStatus.pending_supervisor || 0;
    const totalDocuments = analytics.documentAnalytics.totalDocuments;
    const pendingReviews = analytics.documentAnalytics.documentsByStatus.under_review || 0;
    const totalMilestones = analytics.milestoneAnalytics.totalMilestones;
    const overdueMilestones = analytics.milestoneAnalytics.overdueMilestones || 0;
    
    const userEngagementRate = totalUsers > 0 ? (activeUsers / totalUsers) * 100 : 0;
    const projectCompletionRate = totalProjects > 0 ? (completedProjects / totalProjects) * 100 : 0;
    const documentApprovalRate = analytics.documentAnalytics.documentsCompletionRate || 0;
    const milestoneCompletionRate = analytics.milestoneAnalytics.milestonesCompletionRate || 0;
    
    // Calculate supervisor workload distribution
    const supervisorWorkload = analytics.supervisorLoad || [];
    const avgProjectsPerSupervisor = supervisorWorkload.length > 0 ? 
      supervisorWorkload.reduce((sum, s) => sum + s.projectCount, 0) / supervisorWorkload.length : 0;

    return {
      totalUsers,
      activeUsers,
      totalProjects,
      activeProjects,
      completedProjects,
      pendingSupervisorProjects,
      totalDocuments,
      pendingReviews,
      totalMilestones,
      overdueMilestones,
      userEngagementRate,
      projectCompletionRate,
      documentApprovalRate,
      milestoneCompletionRate,
      supervisorWorkload,
      avgProjectsPerSupervisor,
    };
  }, [analytics]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-8 bg-muted rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error || !analytics || !managerMetrics) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>Unable to load comprehensive analytics</p>
            <Button variant="outline" onClick={refreshAnalytics} className="mt-2">
              Try again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const projectStatusData = Object.entries(analytics.projectsByStatus).map(([status, count]) => ({
    name: status.replace('_', ' ').toUpperCase(),
    value: count,
    color: STATUS_COLORS[status as keyof typeof STATUS_COLORS] || "#6b7280"
  }));

  const userRoleData = Object.entries(analytics.userEngagement.usersByRole).map(([role, count]) => ({
    name: role.charAt(0).toUpperCase() + role.slice(1),
    value: count,
    color: role === 'student' ? '#3b82f6' : role === 'supervisor' ? '#10b981' : role === 'manager' ? '#f59e0b' : '#8b5cf6'
  }));

  const monthlyTrendData = analytics.projectsByMonth?.map(month => ({
    month: month.shortMonth,
    created: month.count,
    completed: month.completed,
    active: month.active,
    completionRate: month.completionRate
  })) || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Comprehensive Analytics Dashboard</h2>
          <p className="text-muted-foreground">
            Complete overview of organizational performance and metrics
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-sm">
            {timeRange.charAt(0).toUpperCase() + timeRange.slice(1)} View
          </Badge>
          <Button variant="outline" size="sm" onClick={() => exportAnalytics('json')}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Executive Summary */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{managerMetrics.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              {managerMetrics.activeUsers} active ({managerMetrics.userEngagementRate.toFixed(1)}%)
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{managerMetrics.totalProjects}</div>
            <p className="text-xs text-muted-foreground">
              {managerMetrics.activeProjects} active, {managerMetrics.completedProjects} completed
            </p>
          </CardContent>
        </Card>

        <Card className={managerMetrics.pendingSupervisorProjects > 0 ? "border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/20" : ""}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className={`text-sm font-medium ${managerMetrics.pendingSupervisorProjects > 0 ? "text-amber-700 dark:text-amber-300" : ""}`}>
              Pending Assignment
            </CardTitle>
            <Calendar className={`h-4 w-4 ${managerMetrics.pendingSupervisorProjects > 0 ? "text-amber-500" : "text-muted-foreground"}`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${managerMetrics.pendingSupervisorProjects > 0 ? "text-amber-600 dark:text-amber-400" : ""}`}>
              {managerMetrics.pendingSupervisorProjects}
            </div>
            <p className={`text-xs ${managerMetrics.pendingSupervisorProjects > 0 ? "text-amber-600 dark:text-amber-400" : "text-muted-foreground"}`}>
              projects need supervisors
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Documents</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{managerMetrics.totalDocuments}</div>
            <p className="text-xs text-muted-foreground">
              {managerMetrics.pendingReviews} pending review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{managerMetrics.projectCompletionRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              project completion
            </p>
          </CardContent>
        </Card>

        <Card className={managerMetrics.overdueMilestones > 0 ? "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20" : ""}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className={`text-sm font-medium ${managerMetrics.overdueMilestones > 0 ? "text-red-700 dark:text-red-300" : ""}`}>
              {managerMetrics.overdueMilestones > 0 ? "Overdue Items" : "On Track"}
            </CardTitle>
            {managerMetrics.overdueMilestones > 0 ? (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            ) : (
              <CheckCircle2 className="h-4 w-4 text-green-500" />
            )}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${managerMetrics.overdueMilestones > 0 ? "text-red-600 dark:text-red-400" : "text-green-600 dark:text-green-400"}`}>
              {managerMetrics.overdueMilestones > 0 ? managerMetrics.overdueMilestones : "✓"}
            </div>
            <p className={`text-xs ${managerMetrics.overdueMilestones > 0 ? "text-red-600 dark:text-red-400" : "text-muted-foreground"}`}>
              {managerMetrics.overdueMilestones > 0 ? "milestones overdue" : "All on schedule"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Overview */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Project Trends Over Time</CardTitle>
            <CardDescription>Monthly project creation and completion trends</CardDescription>
          </CardHeader>
          <CardContent>
            {monthlyTrendData.length > 0 ? (
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={monthlyTrendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area type="monotone" dataKey="created" stackId="1" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.6} name="Created" />
                    <Area type="monotone" dataKey="completed" stackId="1" stroke="#10b981" fill="#10b981" fillOpacity={0.6} name="Completed" />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <BarChart3 className="h-8 w-8 mx-auto mb-2" />
                <p>No trend data available</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Supervisor Workload Distribution</CardTitle>
            <CardDescription>Project distribution across supervisors</CardDescription>
          </CardHeader>
          <CardContent>
            {managerMetrics.supervisorWorkload.length > 0 ? (
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={managerMetrics.supervisorWorkload} layout="vertical">
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="supervisorName" type="category" width={100} />
                    <Tooltip />
                    <Bar dataKey="projectCount" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Users className="h-8 w-8 mx-auto mb-2" />
                <p>No supervisor data available</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Status Distributions */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Project Status Distribution</CardTitle>
            <CardDescription>Current status breakdown of all projects</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[250px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={projectStatusData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {projectStatusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>User Role Distribution</CardTitle>
            <CardDescription>Breakdown of users by role</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[250px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={userRoleData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {userRoleData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Key Performance Indicators</CardTitle>
            <CardDescription>Critical metrics at a glance</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">User Engagement</span>
                <span className="text-lg font-bold text-blue-600">
                  {managerMetrics.userEngagementRate.toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Project Completion</span>
                <span className="text-lg font-bold text-green-600">
                  {managerMetrics.projectCompletionRate.toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Document Approval</span>
                <span className="text-lg font-bold text-purple-600">
                  {managerMetrics.documentApprovalRate.toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Milestone Completion</span>
                <span className="text-lg font-bold text-orange-600">
                  {managerMetrics.milestoneCompletionRate.toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Avg Projects/Supervisor</span>
                <span className="text-lg font-bold text-indigo-600">
                  {managerMetrics.avgProjectsPerSupervisor.toFixed(1)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
