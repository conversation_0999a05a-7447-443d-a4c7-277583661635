'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DocumentViewer } from '@/components/documents/DocumentViewer';
import { DocumentWorkflowStatus } from '@/components/documents/DocumentWorkflowStatus';
import { PDFViewer } from '@/components/documents/PdfViewer';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Calendar,
  Clock,
  Users,
  MessageCircle,
  AlertCircle,
  FileText
} from 'lucide-react';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { Comment, DocumentStatus, Document as DocumentType } from '@/lib/types';
import { useDocument } from '@/hooks/useDocument';
import { useComment } from '@/hooks/useComment';
import { useUser } from '@/hooks/useUser';
import { useAuth } from '@/hooks/useAuth';
import { useProject } from '@/hooks/useProject';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/useToast';
import React, { useState, useMemo, useCallback } from 'react';
import { CommentThread } from '../comments/CommentThread';
import { getFileTypeFromUrl, getFileInfo } from '@/lib/utils/file-utils';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

type DocumentPageProps = {
  id: string;
};

// Enhanced skeleton components with better styling
const DocumentSkeleton = () => (
  <div className="space-y-8">
    {/* Header skeleton */}
    <div className="flex flex-col lg:flex-row lg:items-start justify-between gap-6">
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <Skeleton className="h-8 w-80 rounded-lg" />
          <Skeleton className="h-6 w-20 rounded-full" />
        </div>
        <div className="flex flex-wrap gap-4">
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-4 rounded" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-4 rounded" />
            <Skeleton className="h-4 w-36" />
          </div>
        </div>
      </div>
      <div className="flex gap-2">
        <Skeleton className="h-9 w-28 rounded-md" />
        <Skeleton className="h-9 w-32 rounded-md" />
      </div>
    </div>

    {/* Content skeleton */}
    <Card className="shadow-sm border-0 bg-gradient-to-br from-background to-muted/20">
      <CardContent className="p-8">
        <div className="space-y-6">
          <div className="flex gap-4 mb-6">
            <Skeleton className="h-10 w-40 rounded-lg" />
            <Skeleton className="h-10 w-36 rounded-lg" />
          </div>
          <Skeleton className="h-96 w-full rounded-xl" />
        </div>
      </CardContent>
    </Card>
  </div>
);

const PeopleSkeleton = () => (
  <Card className="shadow-sm border-0 bg-gradient-to-br from-background to-muted/20">
    <CardHeader className="pb-4">
      <CardTitle className="flex items-center gap-2 text-lg">
        <Users className="h-5 w-5 text-primary" />
        <span>People</span>
      </CardTitle>
    </CardHeader>
    <CardContent className="space-y-6">
      {/* Student skeleton */}
      <div className="space-y-3">
        <Skeleton className="h-4 w-16 rounded" />
        <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
          <div className="flex items-center gap-3">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-24" />
            </div>
          </div>
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
      </div>

      {/* Supervisors skeleton */}
      <div className="space-y-3">
        <Skeleton className="h-4 w-20 rounded" />
        <div className="space-y-3">
          {[1, 2].map(i => (
            <div key={i} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
              <div className="flex items-center gap-3">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-28" />
                  <Skeleton className="h-3 w-32" />
                </div>
              </div>
              <Skeleton className="h-8 w-8 rounded-full" />
            </div>
          ))}
        </div>
      </div>
    </CardContent>
  </Card>
);

export function DocumentPage({ id }: DocumentPageProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isUpdating, setIsUpdating] = useState(false);

  // Document data
  const { getById: useDocumentQuery, update: updateDocument } = useDocument();
  const {
    data: document,
    isLoading: isLoadingDocument,
    error: documentError
  } = useDocumentQuery(id);

  // Comments data
  const { getDocumentComments } = useComment();
  const { data: comments = [] } = getDocumentComments(id);

  // Get all users involved in comments
  const { useUsersByIdsQuery } = useUser();
  const userIds = useMemo(() => {
    if (!comments || comments.length === 0) return [];
    const ids = Array.from(new Set(comments.map((comment: Comment) => comment.userId)));
    return ids.filter(Boolean) as string[]; // Filter out any null/undefined values
  }, [comments]);

  // Load users data for the comments
  const { isLoading: isLoadingUsers } = useUsersByIdsQuery(userIds);

  // Get student information
  const { useUserQuery } = useUser();
  const { data: student } = useUserQuery(document?.studentId || '');

  // Get supervisors information
  const {
    data: supervisors = [],
    isLoading: isLoadingSupervisors
  } = useUsersByIdsQuery(document?.supervisorIds || []);

  // Get project data to access team members
  const { useProjectQuery } = useProject();
  const {
    data: project
  } = useProjectQuery(document?.projectId || '');

  // Check if the current user can change the document status
  const { user } = useAuth();
  const canChangeStatus = useMemo(() => {
    if (!user || !document || !project) return false;

    // Team member supervisors can approve/reject documents
    const isTeamMember = project.teamMembers?.includes(user.id);
    const isSupervisor = user.role === 'supervisor';
    if (isTeamMember && isSupervisor) {
      return true;
    }

    // Team member students can submit their documents for review
    const isStudent = user.role === 'student';
    if (isTeamMember && isStudent) {
      return document.status === 'rejected' || document.status === 'draft';
    }

    // Admins can change any document status
    if (user.role === 'admin') {
      return true;
    }

    return false;
  }, [user, document, project]);

  // Define all hooks first, before any conditional returns

  // Utility functions
  const hasValidPdf = useCallback((doc: DocumentType | null | undefined): boolean => {
    if (!doc || !doc.fileUrl) return false;
    return getFileTypeFromUrl(doc.fileUrl) === 'pdf';
  }, []);

  const formatDate = useCallback((date: Date | string | undefined): string => {
    if (!date) return 'Unknown';
    try {
      return format(new Date(date), 'PPP');
    } catch (error) {
      return 'Invalid date';
    }
  }, []);

  const getFileTypeLabel = useCallback((fileUrl: string | undefined): string => {
    if (!fileUrl) return 'Unknown';
    const fileInfo = getFileInfo(fileUrl);
    return fileInfo.label;
  }, []);

  // Handler functions

  const handleChat = useCallback((userId: string): void => {
    if (!userId) return;
    router.push(`/chat?user=${userId}`);
  }, [router]);

  const handleStatusChange = useCallback(async (newStatus: DocumentStatus, feedback?: string): Promise<void> => {
    if (!document) return;

    try {
      setIsUpdating(true);

      // Create update payload with proper typing
      const updatePayload: Partial<DocumentType> & { id: string } = {
        id: document.id,
        status: newStatus
      };

      // Add feedback if provided (for rejections)
      if (feedback && newStatus === 'rejected') {
        updatePayload.feedback = feedback;
      }

      // Update the document
      await updateDocument.mutateAsync(updatePayload);

      // Prepare status message
      const statusTitle = {
        'approved': 'Document Approved',
        'rejected': 'Document Rejected',
        'under_review': 'Document Submitted for Review',
        'draft': 'Document Saved as Draft'
      }[newStatus] || 'Status Updated';

      const statusDescription = {
        'approved': 'The document has been approved successfully.',
        'rejected': 'The document has been rejected.',
        'under_review': 'Document has been submitted for review.',
        'draft': 'Document has been saved as draft.'
      }[newStatus] || `The document status has been updated to ${newStatus.replace('_', ' ')}.`;

      // Show success message
      toast({
        title: statusTitle,
        description: statusDescription
      });
    } catch (error) {
      console.error('Status update error:', error);
      toast({
        variant: "destructive",
        title: "Update failed",
        description: "There was an error updating the document status."
      });
    } finally {
      setIsUpdating(false);
    }
  }, [document, updateDocument, toast]);

  // Enhanced page layout component with better styling
  const PageLayout = useCallback(({ children }: { children: React.ReactNode }) => (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/10">
      <main className="container max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {children}
      </main>
    </div>
  ), []);

  // Enhanced loading state with better visual hierarchy
  if (isLoadingDocument) {
    return (
      <PageLayout>
        <div className="space-y-8">
          {/* Breadcrumb skeleton */}
          <div className="flex items-center gap-2 text-sm">
            <Skeleton className="h-4 w-20" />
            <span className="text-muted-foreground">/</span>
            <Skeleton className="h-4 w-24" />
            <span className="text-muted-foreground">/</span>
            <Skeleton className="h-4 w-32" />
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* Main content area */}
            <div className="xl:col-span-2 space-y-8">
              <DocumentSkeleton />
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Workflow status skeleton */}
              <Card className="shadow-sm border-0 bg-gradient-to-br from-background to-muted/20">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Clock className="h-5 w-5 text-primary" />
                    <span>Document Workflow</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Skeleton className="h-8 w-full rounded-lg" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                </CardContent>
              </Card>

              <PeopleSkeleton />

              {/* Comments skeleton */}
              <Card className="shadow-sm border-0 bg-gradient-to-br from-background to-muted/20">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <MessageCircle className="h-5 w-5 text-primary" />
                    <span>Comments</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {[1, 2, 3].map(i => (
                    <div key={i} className="flex gap-3 p-3 rounded-lg bg-muted/30">
                      <Skeleton className="h-8 w-8 rounded-full flex-shrink-0" />
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center gap-2">
                          <Skeleton className="h-3 w-20" />
                          <Skeleton className="h-3 w-16" />
                        </div>
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Enhanced error state with better visual design
  if (documentError) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center space-y-6 max-w-md">
            <div className="mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center">
              <AlertCircle className="h-8 w-8 text-destructive" />
            </div>
            <div className="space-y-2">
              <h1 className="text-2xl font-bold tracking-tight">Error Loading Document</h1>
              <p className="text-muted-foreground">
                There was an error loading the document. Please try again later or contact support.
              </p>
              {documentError instanceof Error && (
                <details className="mt-4 p-3 bg-muted/50 rounded-lg text-left">
                  <summary className="text-sm font-medium cursor-pointer">Error details</summary>
                  <p className="mt-2 text-sm text-muted-foreground font-mono">
                    {documentError.message}
                  </p>
                </details>
              )}
            </div>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="min-w-[120px]"
              >
                Go Back
              </Button>
              <Button
                onClick={() => router.push('/documents')}
                className="min-w-[120px]"
              >
                View All Documents
              </Button>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Enhanced not found state
  if (!document) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center space-y-6 max-w-md">
            <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center">
              <FileText className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h1 className="text-2xl font-bold tracking-tight">Document Not Found</h1>
              <p className="text-muted-foreground">
                The document you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="min-w-[120px]"
              >
                Go Back
              </Button>
              <Button
                onClick={() => router.push('/documents')}
                className="min-w-[120px]"
              >
                View All Documents
              </Button>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }



  // Enhanced main render with improved layout and styling
  return (
    <PageLayout>
      <div className="space-y-8">
        {/* Breadcrumb navigation */}
        <nav className="flex items-center gap-2 text-sm text-muted-foreground">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/documents')}
            className="h-auto p-0 hover:text-foreground"
          >
            Documents
          </Button>
          <span>/</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/projects/${document.projectId}`)}
            className="h-auto p-0 hover:text-foreground"
          >
            Project
          </Button>
          <span>/</span>
          <span className="text-foreground font-medium truncate max-w-[200px]">
            {document.title}
          </span>
        </nav>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Main content area */}
          <div className="xl:col-span-2 space-y-8">
            {/* Enhanced document header */}
            <div className="space-y-6">
              <div className="flex flex-col lg:flex-row lg:items-start justify-between gap-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <h1 className="text-3xl font-bold tracking-tight">{document.title}</h1>
                    <DocumentWorkflowStatus
                      currentStatus={document.status as DocumentStatus}
                      onStatusChange={handleStatusChange}
                      isUpdating={isUpdating}
                      canChangeStatus={canChangeStatus}
                      compact={true}
                    />
                  </div>

                  <div className="flex flex-wrap gap-6 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2" title="Creation date">
                      <Calendar className="h-4 w-4" />
                      <span>Created {formatDate(document.createdAt as string | Date)}</span>
                    </div>
                    <div className="flex items-center gap-2" title="Last update date">
                      <Clock className="h-4 w-4" />
                      <span>Updated {formatDate(document.updatedAt as string | Date)}</span>
                    </div>
                  </div>
                </div>

                {/* Action buttons */}
                <div className="flex flex-wrap gap-3">
                  {/* Show Edit button for draft documents owned by current user */}
                  {document.status === 'draft' && user?.id === document.studentId && (
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => router.push(`/documents/${document.id}/edit`)}
                      className="min-w-[120px]"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Edit Document
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push(`/projects/${document.projectId}`)}
                    className="min-w-[120px]"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    View Project
                  </Button>
                </div>
              </div>
            </div>

            {/* Enhanced document content and attachment tabs */}
            <Card className="shadow-sm border-0 bg-gradient-to-br from-background to-muted/20">
              <CardContent className="p-8">
                <Tabs defaultValue={hasValidPdf(document) ? "attachment" : "content"} className="w-full">
                  <div className="flex items-center justify-between mb-6">
                    <TabsList className="grid w-full grid-cols-2 lg:w-auto">
                      <TabsTrigger
                        value="content"
                        disabled={!document.content}
                        className="flex items-center gap-2"
                        aria-label="View document content"
                      >
                        <FileText className="h-4 w-4" />
                        <span className="hidden sm:inline">Document Content</span>
                        <span className="sm:hidden">Content</span>
                      </TabsTrigger>
                      <TabsTrigger
                        value="attachment"
                        disabled={!document.fileUrl}
                        className="flex items-center gap-2"
                        aria-label="View PDF attachment"
                      >
                        <FileText className="h-4 w-4" />
                        <span className="hidden sm:inline">PDF Attachment</span>
                        <span className="sm:hidden">PDF</span>
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  {/* Document content tab */}
                  <TabsContent value="content" className="mt-0">
                    {document.content ? (
                      <div className="prose prose-sm max-w-none dark:prose-invert">
                        <DocumentViewer document={document} />
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-16 text-center">
                        <div className="w-16 h-16 rounded-full bg-muted/50 flex items-center justify-center mb-4">
                          <FileText className="h-8 w-8 text-muted-foreground" />
                        </div>
                        <h3 className="text-xl font-semibold mb-2">No content available</h3>
                        <p className="text-muted-foreground max-w-md">
                          This document doesn&apos;t have any text content. Please check the attachment tab for PDF files.
                        </p>
                      </div>
                    )}
                  </TabsContent>

                  {/* Attachment tab */}
                  <TabsContent value="attachment" className="mt-0">
                    {document.fileUrl ? (
                      <div className="space-y-4">
                        <div className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg">
                          <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                            <FileText className="h-5 w-5 text-primary" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium">{document.title}</h4>
                            <p className="text-sm text-muted-foreground">
                              {getFileTypeLabel(document.fileUrl)}
                            </p>
                          </div>
                        </div>

                        <div className="border rounded-xl overflow-hidden bg-background shadow-sm">
                          {getFileTypeFromUrl(document.fileUrl) === 'pdf' ? (
                            <PDFViewer
                              fileUrl={document.fileUrl}
                              fileName={document.title}
                              maxHeight={700}
                            />
                          ) : (
                            <div className="flex flex-col items-center justify-center py-16 text-center">
                              <div className="w-16 h-16 rounded-full bg-destructive/10 flex items-center justify-center mb-4">
                                <AlertCircle className="h-8 w-8 text-destructive" />
                              </div>
                              <h3 className="text-xl font-semibold mb-2">Unsupported File Type</h3>
                              <p className="text-muted-foreground max-w-md">
                                This document contains a file that is not supported. Only PDF files are allowed for viewing.
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-16 text-center">
                        <div className="w-16 h-16 rounded-full bg-muted/50 flex items-center justify-center mb-4">
                          <FileText className="h-8 w-8 text-muted-foreground" />
                        </div>
                        <h3 className="text-xl font-semibold mb-2">No attachment available</h3>
                        <p className="text-muted-foreground max-w-md">
                          This document doesn&apos;t have any file attachments. Please check the content tab for text content.
                        </p>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced sidebar */}
          <div className="space-y-6">
            {/* Enhanced Document Workflow Status Card */}
            <Card className="shadow-sm border-0 bg-gradient-to-br from-background to-muted/20">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Clock className="h-5 w-5 text-primary" />
                  <span>Document Workflow</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <DocumentWorkflowStatus
                  currentStatus={document.status as DocumentStatus}
                  onStatusChange={handleStatusChange}
                  isUpdating={isUpdating}
                  canChangeStatus={canChangeStatus}
                />

                {/* Enhanced feedback display */}
                {document.feedback && document.status === 'rejected' && (
                  <div className="p-4 border rounded-lg bg-red-50 border-red-200 dark:bg-red-950/30 dark:border-red-800">
                    <h4 className="text-sm font-semibold mb-2 text-red-700 dark:text-red-400 flex items-center gap-2">
                      <AlertCircle className="h-4 w-4" />
                      Rejection Feedback
                    </h4>
                    <p className="text-sm text-red-600 dark:text-red-300 leading-relaxed">
                      {document.feedback}
                    </p>
                  </div>
                )}

                {/* Enhanced file type warning */}
                {document.fileUrl && !hasValidPdf(document) && (
                  <div className="p-4 border rounded-lg bg-amber-50 border-amber-200 dark:bg-amber-950/30 dark:border-amber-800">
                    <h4 className="text-sm font-semibold mb-2 text-amber-700 dark:text-amber-400 flex items-center gap-2">
                      <AlertCircle className="h-4 w-4" />
                      Unsupported File Type
                    </h4>
                    <p className="text-sm text-amber-600 dark:text-amber-300 leading-relaxed">
                      This document contains a file that is not supported. Only PDF files are allowed.
                      Please upload a PDF file to ensure proper functionality.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Enhanced People Card */}
            <Card className="shadow-sm border-0 bg-gradient-to-br from-background to-muted/20">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Users className="h-5 w-5 text-primary" />
                  <span>People</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {isLoadingUsers || isLoadingSupervisors ? (
                  <div className="space-y-4">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
                        <Skeleton className="h-12 w-12 rounded-full" />
                        <div className="flex-1 space-y-2">
                          <Skeleton className="h-4 w-32" />
                          <Skeleton className="h-3 w-24" />
                        </div>
                        <Skeleton className="h-8 w-8 rounded-full" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <>
                    {student && (
                      <div className="space-y-3">
                        <h3 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                          Student
                        </h3>
                        <div className="p-4 rounded-lg bg-muted/30 hover:bg-muted/40 transition-colors">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Avatar className="h-12 w-12 ring-2 ring-primary/20">
                                <AvatarImage src={student.profileImage} alt={student.name || 'Student'} />
                                <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                                  {student.name?.slice(0, 2)?.toUpperCase() || 'ST'}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="font-semibold">{student.name}</p>
                                <p className="text-sm text-muted-foreground">
                                  {student.department || 'No department'}
                                </p>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleChat(student.id)}
                              title={`Chat with ${student.name}`}
                              aria-label={`Chat with ${student.name}`}
                              className="h-10 w-10 rounded-full hover:bg-primary/10 hover:text-primary"
                            >
                              <MessageCircle className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}

                    {supervisors.length > 0 && (
                      <div className="space-y-3">
                        <h3 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                          Supervisors ({supervisors.length})
                        </h3>
                        <div className="space-y-3">
                          {supervisors.map(supervisor => (
                            <div
                              key={supervisor.id}
                              className="p-4 rounded-lg bg-muted/30 hover:bg-muted/40 transition-colors"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <Avatar className="h-12 w-12 ring-2 ring-primary/20">
                                    <AvatarImage src={supervisor.profileImage} alt={supervisor.name || 'Supervisor'} />
                                    <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                                      {supervisor.name?.slice(0, 2)?.toUpperCase() || 'SV'}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <p className="font-semibold">{supervisor.name}</p>
                                    <p className="text-sm text-muted-foreground">
                                      {supervisor.specialization || 'No specialization'}
                                    </p>
                                  </div>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleChat(supervisor.id)}
                                  title={`Chat with ${supervisor.name}`}
                                  aria-label={`Chat with ${supervisor.name}`}
                                  className="h-10 w-10 rounded-full hover:bg-primary/10 hover:text-primary"
                                >
                                  <MessageCircle className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>

            {/* Enhanced Comments section */}
            <CommentThread
              documentId={id}
              highlightId={undefined}
              showThread={true}
              documentStatus={document?.status}
              projectTeamMembers={project?.teamMembers || []}
            />
          </div>
        </div>
      </div>


    </PageLayout>
  );
}



















