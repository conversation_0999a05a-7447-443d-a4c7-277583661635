"use client";

import { useAuth } from "@/hooks/useAuth";
import { useRealtimeChat } from "@/hooks/useRealtimeChat";
import { useChat } from "@/hooks/useChat";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ConnectionStatus } from "@/components/chat/ConnectionStatus";
import { Badge } from "@/components/ui/badge";

export default function ChatDebugPage() {
  const { user } = useAuth();
  const userId = user?.id;
  const [testRecipientId] = useState("test-recipient-id");
  const [logs, setLogs] = useState<string[]>([]);
  const [messageCount, setMessageCount] = useState(0);

  const { useChatMessages, sendMessage, addOptimisticMessage } = useChat();

  const {
    data: messagesData,
    isLoading: isLoadingChat,
    isError: isMessagesError,
  } = useChatMessages(testRecipientId, userId);

  const { isConnected, connectionStatus, reconnect } = useRealtimeChat({
    userId: userId || "",
    recipientId: testRecipientId,
    enabled: !!userId,
    onMessageReceived: () => {
      addLog("Real-time message received callback triggered");
    },
    onMessageDeleted: () => {
      addLog("Real-time message deleted callback triggered");
    }
  });

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)]);
  };

  const sendTestMessage = async () => {
    if (!userId) return;
    
    try {
      const messageContent = `Test message ${messageCount + 1}`;
      addLog(`Sending test message: ${messageContent}`);
      
      const optimisticId = addOptimisticMessage(userId, testRecipientId, messageContent);
      addLog(`Added optimistic message with ID: ${optimisticId}`);
      
      await sendMessage.mutateAsync({
        senderId: userId,
        recipientId: testRecipientId,
        content: messageContent,
        type: 'text',
        optimisticId
      });
      
      setMessageCount(prev => prev + 1);
      addLog(`Successfully sent message: ${messageContent}`);
    } catch (error) {
      addLog(`Error sending message: ${error}`);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  useEffect(() => {
    addLog(`Chat debug page loaded. User ID: ${userId}`);
  }, [userId]);

  useEffect(() => {
    addLog(`Connection status changed to: ${connectionStatus}`);
  }, [connectionStatus]);

  useEffect(() => {
    if (messagesData && typeof messagesData === 'object' && messagesData !== null && 'pages' in messagesData) {
      const pages = (messagesData as any).pages;
      if (Array.isArray(pages)) {
        const totalMessages = pages.reduce((total: number, page: any) => total + (page?.messages?.length || 0), 0);
        addLog(`Messages data updated. Total messages: ${totalMessages}`);
      }
    }
  }, [messagesData]);

  if (!userId) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardContent className="p-6">
            <p>Please log in to use the chat debug page.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const totalMessages = (messagesData && typeof messagesData === 'object' && messagesData !== null && 'pages' in messagesData && Array.isArray((messagesData as any).pages))
    ? (messagesData as any).pages.reduce((total: number, page: any) => total + (page?.messages?.length || 0), 0)
    : 0;

  return (
    <div className="container mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Chat Real-time Debug</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <p className="text-sm font-medium">User ID</p>
              <Badge variant="outline">{userId}</Badge>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Connection Status</p>
              <ConnectionStatus
                isConnected={isConnected}
                connectionStatus={connectionStatus}
                onReconnect={reconnect}
              />
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Messages Loaded</p>
              <Badge variant="outline">{totalMessages}</Badge>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Loading State</p>
              <Badge variant={isLoadingChat ? "destructive" : "default"}>
                {isLoadingChat ? "Loading" : "Ready"}
              </Badge>
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={sendTestMessage} disabled={!isConnected || sendMessage.isPending}>
              {sendMessage.isPending ? "Sending..." : "Send Test Message"}
            </Button>
            <Button onClick={reconnect} variant="outline">
              Force Reconnect
            </Button>
            <Button onClick={clearLogs} variant="outline">
              Clear Logs
            </Button>
          </div>

          {isMessagesError && (
            <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-red-800 dark:text-red-200">Error loading messages</p>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Debug Logs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="max-h-96 overflow-y-auto space-y-1 font-mono text-sm">
            {logs.length === 0 ? (
              <p className="text-muted-foreground">No logs yet...</p>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="p-2 bg-muted rounded text-xs">
                  {log}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
