import { create } from 'zustand';

interface FormState<T> {
  data: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  setField: <K extends keyof T>(field: K, value: T[K]) => void;
  setError: <K extends keyof T>(field: K, error: string) => void;
  clearError: <K extends keyof T>(field: K) => void;
  reset: () => void;
  isValid: () => boolean;
}

export const createFormStore = <T extends object>(initialData: T) => 
  create<FormState<T>>((set, get) => ({
    data: initialData,
    errors: {},
    touched: {},
    setField: (field, value) => 
      set((state) => ({
        data: { ...state.data, [field]: value },
        touched: { ...state.touched, [field]: true },
        // Clear error when field is updated
        errors: { ...state.errors, [field]: undefined },
      })),
    setError: (field, error) =>
      set((state) => ({
        errors: { ...state.errors, [field]: error },
      })),
    clearError: (field) =>
      set((state) => ({
        errors: { ...state.errors, [field]: undefined },
      })),
    reset: () => 
      set({ data: initialData, errors: {}, touched: {} }),
    isValid: () => Object.values(get().errors).every(error => !error),
  }));
