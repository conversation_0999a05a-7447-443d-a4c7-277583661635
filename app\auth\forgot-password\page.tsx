'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/useToast';
import { useAuth } from '@/hooks/useAuth';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { CheckCircle } from 'lucide-react';

export default function ForgotPasswordPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { requestReset, isLoading } = useAuth();
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  // Add password strength validation similar to reset-password page
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Memoize the submit handler to prevent recreation on each render
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Enhanced email validation
    if (!email.trim() || !validateEmail(email.trim())) {
      toast({
        variant: "destructive",
        title: "Invalid email",
        description: "Please enter a valid email address",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await requestReset(email);
      setSubmitted(true);
      toast({
        title: "Reset link sent",
        description: "If an account exists with this email, you will receive a password reset link",
      });
    } catch (error) {
      // Still set submitted to true to prevent email enumeration
      setSubmitted(true);
      // Don't show error to prevent email enumeration attacks
    } finally {
      setIsSubmitting(false);
    }
  }, [email, toast, requestReset]);

  // Show loading state while auth is initializing
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-muted/40">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Reset Password</CardTitle>
          <CardDescription>
            {!submitted 
              ? "Enter your email address and we'll send you a link to reset your password" 
              : "Check your email for a reset link"}
          </CardDescription>
        </CardHeader>
        
        {!submitted ? (
          <>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4" aria-label="Password reset request form">
                <div className="space-y-2">
                  <label htmlFor="email" className="sr-only">Email address</label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isSubmitting}
                    required
                    autoComplete="email"
                    autoFocus
                    aria-label="Email address"
                    aria-describedby="email-hint"
                  />
                  <p id="email-hint" className="text-xs text-muted-foreground">
                    Enter the email address associated with your account
                  </p>
                </div>
                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={isSubmitting}
                  aria-busy={isSubmitting}
                >
                  {isSubmitting ? "Sending..." : "Send Reset Link"}
                </Button>
              </form>
            </CardContent>
            <CardFooter className="flex justify-center">
              <Link href="/auth/login" className="text-sm text-primary hover:underline">
                Back to login
              </Link>
            </CardFooter>
          </>
        ) : (
          <CardContent className="space-y-4">
            <div className="bg-muted p-4 rounded-lg text-center">
              <div className="flex justify-center mb-4">
                <CheckCircle className="h-12 w-12 text-green-500" />
              </div>
              <p className="mb-4">
                If an account exists with the email <strong>{email}</strong>,
                we&apos;ve sent instructions to reset your password.
              </p>
              <p className="text-sm text-muted-foreground">
                Please check your inbox and spam folder.
              </p>
            </div>
            <Button 
              onClick={() => router.push('/auth/login')} 
              className="w-full"
            >
              Return to Login
            </Button>
          </CardContent>
        )}
      </Card>
    </div>
  );
}




