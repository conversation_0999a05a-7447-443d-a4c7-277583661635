import { ChatMessage } from "@/lib/types";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";
import {
  MoreHorizontal,
  Trash,
  Reply,
  Copy,
  FileIcon,
  X,
  Download,
  ZoomIn,
  ZoomOut,
  Maximize,
  Minimize,
  Image as ImageIcon,
  FileText,
  File,
} from "lucide-react";
import { useUser } from "@/hooks/useUser";
import React, {
  useState,
  useRef,
  useEffect,
  useCallback,
  useMemo,
} from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/useToast";
import { useChat } from "@/hooks/useChat";

import { MessageListStatus } from "./MessageStatus";

interface MessageProps {
  message: ChatMessage;
  isOwn: boolean;
  showAvatar?: boolean;
  isFirstInGroup?: boolean;
  isLastInGroup?: boolean;
  status?: "sending" | "sent" | "error" | "delivered" | "read" | "failed";
  onReply?: (message: ChatMessage) => void;
  onDeleteMessage?: (messageId: string) => void;
}

// File type information based on extension
interface FileTypeInfo {
  icon: React.ReactNode;
  label: string;
  color: string;
  iconColor: string;
}



export function Message({
  message,
  isOwn,
  showAvatar = true,
  isFirstInGroup = true,
  isLastInGroup = true,
  status,
  onReply,
  onDeleteMessage: propDeleteMessage,
}: MessageProps) {
  const { data: sender } = useUser().useUserQuery(message.senderId);
  const [showActions, setShowActions] = useState(false);
  const { toast } = useToast();
  const { deleteMessage } = useChat();
  const messageRef = useRef<HTMLDivElement>(null);

  // Image viewer state
  const [isImageViewerOpen, setIsImageViewerOpen] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Get file extension from URL or filename - memoized to avoid recalculation
  const getFileExtension = useCallback((url: string): string => {
    // First try to extract from the URL path
    const urlMatch = url.match(/\.([a-zA-Z0-9]+)(?:\?|$)/);

    // If we found a match in the URL, return it
    if (urlMatch && urlMatch[1]) {
      return urlMatch[1].toLowerCase();
    }

    // If no extension found in URL, try to extract from the filename
    if (message.content) {
      const filenameMatch = message.content.match(/\.([a-zA-Z0-9]+)$/);
      if (filenameMatch && filenameMatch[1]) {
        return filenameMatch[1].toLowerCase();
      }
    }

    // If still no extension found, return empty string
    return "";
  }, [message.content]);

  // Extract file extension once
  const fileExt = useMemo(
    () => (message.fileUrl ? getFileExtension(message.fileUrl) : ""),
    [message.fileUrl, getFileExtension]
  );

  // Determine file name once
  const fileName = useMemo(() => {
    // If content is available and looks like a filename, use it
    if (message.content) {
      // Check if content already has an extension
      if (message.content.match(/\.[a-zA-Z0-9]+$/)) {
        return message.content;
      }
      // If content doesn't have an extension but we have one from the URL, add it
      if (fileExt) {
        return `${message.content}.${fileExt}`;
      }
      // Otherwise just use the content as is
      return message.content;
    }

    // If no content, generate a generic filename with the extension
    return fileExt ? `File.${fileExt}` : "File";
  }, [message.content, fileExt]);

  // Handle message deletion
  const handleDelete = async () => {
    try {
      // Check if this is a file message
      const isFileMessage = message.type === "file" && !!message.fileUrl;

      // Use the prop if provided, otherwise use the hook
      if (propDeleteMessage) {
        propDeleteMessage(message.id);
      } else {
        await deleteMessage.mutateAsync(message.id);
      }

      // Show appropriate toast message
      if (isFileMessage) {
        toast({
          title: "Message and file deleted",
          description: "Your message and associated file have been deleted",
        });
      } else {
        toast({
          title: "Message deleted",
          description: "Your message has been deleted",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete message",
        variant: "destructive",
      });
    }
  };

  // Handle message copy
  const handleCopy = useCallback(() => {
    navigator.clipboard.writeText(message.content);
    toast({
      title: "Copied",
      description: "Message copied to clipboard",
    });
  }, [message.content, toast]);

  // Handle message reply
  const handleReply = useCallback(() => {
    if (onReply) {
      onReply(message);
    }
  }, [onReply, message]);



  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        messageRef.current &&
        !messageRef.current.contains(event.target as Node)
      ) {
        setShowActions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle image click to open viewer
  const handleImageClick = useCallback(() => {
    setIsImageViewerOpen(true);
    setZoomLevel(1); // Reset zoom level when opening
  }, []);

  // Handle close image viewer
  const handleCloseImageViewer = useCallback(() => {
    setIsImageViewerOpen(false);
  }, []);

  // Handle zoom in
  const handleZoomIn = useCallback(() => {
    setZoomLevel((prev) => Math.min(prev + 0.25, 3));
  }, []);

  // Handle zoom out
  const handleZoomOut = useCallback(() => {
    setZoomLevel((prev) => Math.max(prev - 0.25, 0.5));
  }, []);

  // Handle fullscreen toggle
  const handleFullscreenToggle = useCallback(() => {
    setIsFullscreen((prev) => !prev);
  }, []);

  // Handle download file
  const handleDownloadFile = useCallback(() => {
    if (!message.fileUrl) return;

    // Show loading toast
    toast({
      title: "Preparing download...",
      description: "Your file is being prepared for download.",
    });

    // Fetch the file as a blob
    fetch(message.fileUrl)
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.blob();
      })
      .then(blob => {
        // Create a blob URL for the file
        const blobUrl = URL.createObjectURL(blob);

        // Create a temporary link element
        const link = document.createElement("a");
        link.href = blobUrl;
        link.download = fileName;

        // Append to body, click, and remove
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the blob URL
        setTimeout(() => URL.revokeObjectURL(blobUrl), 100);

        // Show success toast
        toast({
          title: "Download started",
          description: `Your ${message.type === "image" ? "image" : "file"} is downloading`,
        });
      })
      .catch(error => {
        console.error("Download error:", error);
        toast({
          title: "Download failed",
          description: "There was a problem downloading your file. Please try again.",
          variant: "destructive",
        });
      });
  }, [message.fileUrl, message.type, fileName, toast]);

  // Check if message is an image
  const isImageMessage = useMemo(() => {
    // Check if message type is explicitly 'image'
    if (message.type === "image" && !!message.fileUrl) {
      return true;
    }

    // Check if it's a file message with an image extension
    if (message.type === "file" && !!message.fileUrl) {
      return !!message.fileUrl.match(/\.(jpeg|jpg|gif|png|webp|svg)$/i);
    }

    return false;
  }, [message.type, message.fileUrl]);

  // Get file type info based on extension
  const getFileTypeInfo = useCallback(
    (ext: string): FileTypeInfo => {
      const extension = ext.toLowerCase();

      // Special case for the NVIDIA Quadro file shown in the reference image
      if (
        fileName.toLowerCase().includes("quadro") ||
        fileName.toLowerCase().includes("nvidia")
      ) {
        return {
          icon: (
            <div className="flex flex-col items-center">
              <div className="text-white font-bold text-xs">PDF</div>
              <FileText className="h-5 w-5 text-white mt-1" />
            </div>
          ),
          label: "PDF Document",
          color: "bg-[#76b900]", // NVIDIA green
          iconColor: "text-white",
        };
      } else if (["pdf"].includes(extension)) {
        return {
          icon: (
            <div className="flex flex-col items-center">
              <div className="text-white font-bold text-xs">PDF</div>
              <FileText className="h-5 w-5 text-white mt-1" />
            </div>
          ),
          label: "PDF Document",
          color: "bg-red-600",
          iconColor: "text-white",
        };
      } else if (["doc", "docx"].includes(extension)) {
        return {
          icon: (
            <div className="flex flex-col items-center">
              <div className="text-white font-bold text-xs">DOC</div>
              <File className="h-5 w-5 text-white mt-1" />
            </div>
          ),
          label: "Microsoft Word Document",
          color: "bg-[#2b579a]", // Microsoft Word blue
          iconColor: "text-white",
        };
      } else if (["txt", "rtf"].includes(extension)) {
        return {
          icon: <FileText className="h-6 w-6 text-white" />,
          label: "Text Document",
          color: "bg-blue-600",
          iconColor: "text-white",
        };
      } else if (["xls", "xlsx"].includes(extension)) {
        return {
          icon: (
            <div className="flex flex-col items-center">
              <div className="text-white font-bold text-xs">XLS</div>
              <File className="h-5 w-5 text-white mt-1" />
            </div>
          ),
          label: "Microsoft Excel Spreadsheet",
          color: "bg-[#217346]", // Microsoft Excel green
          iconColor: "text-white",
        };
      } else if (["csv"].includes(extension)) {
        return {
          icon: <FileText className="h-6 w-6 text-white" />,
          label: "CSV Spreadsheet",
          color: "bg-green-600",
          iconColor: "text-white",
        };
      } else if (["ppt", "pptx"].includes(extension)) {
        return {
          icon: (
            <div className="flex flex-col items-center">
              <div className="text-white font-bold text-xs">PPT</div>
              <File className="h-5 w-5 text-white mt-1" />
            </div>
          ),
          label: "Microsoft PowerPoint Presentation",
          color: "bg-[#d24726]", // Microsoft PowerPoint orange
          iconColor: "text-white",
        };
      } else if (["zip", "rar", "7z", "tar", "gz"].includes(extension)) {
        return {
          icon: <FileIcon className="h-6 w-6 text-white" />,
          label: "Archive",
          color: "bg-purple-600",
          iconColor: "text-white",
        };
      } else if (extension) {
        // For known extensions that aren't specifically handled above
        return {
          icon: <FileIcon className="h-6 w-6 text-white" />,
          label: `${extension.toUpperCase()} File`,
          color: "bg-gray-600",
          iconColor: "text-white",
        };
      } else {
        // For completely unknown file types
        return {
          icon: <FileIcon className="h-6 w-6 text-white" />,
          label: "File",
          color: "bg-gray-600",
          iconColor: "text-white",
        };
      }
    },
    [fileName]
  );

  // Render image content
  const renderImageContent = useCallback(() => {
    if (!message.fileUrl) return null;

    const imageFileName = message.content || `Image.${fileExt}`;
    const timestamp = format(new Date(message.timestamp), "HH:mm");

    return (
      <div className="mt-1 max-w-full">
        {/* Display the actual image directly - WhatsApp style */}
        <div
          className={cn(
            "rounded-lg overflow-hidden cursor-pointer relative transition-all duration-300",
            isOwn ? "ml-auto" : "mr-auto"
          )}
          onClick={handleImageClick}
        >
          {/* Loading state */}
          {!imageLoaded && (
            <div className="absolute inset-0 bg-[#f0f2f5] flex items-center justify-center animate-pulse">
              <ImageIcon className="h-8 w-8 text-[#54656f]" />
            </div>
          )}

          {/* The actual image */}
          <img
            src={message.fileUrl}
            alt={imageFileName}
            className={cn(
              "max-w-full max-h-[350px] object-contain transition-all duration-300",
              imageLoaded ? "opacity-100 scale-100" : "opacity-0 scale-95"
            )}
            onLoad={() => setImageLoaded(true)}
          />

          {/* WhatsApp style timestamp overlay - always visible */}
          <div className="absolute bottom-2 right-2 bg-black/40 backdrop-blur-sm text-white text-xs px-1.5 py-0.5 rounded">
            {timestamp}
          </div>

          {/* Overlay with file info and controls - only visible on hover */}
          <div className="absolute bottom-0 left-0 right-0 bg-black/60 backdrop-blur-sm text-white text-xs p-2 flex items-center justify-between opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="flex items-center gap-2">
              <ImageIcon className="h-4 w-4 text-white/90" />
              <span className="text-sm text-white truncate max-w-[150px]">
                {imageFileName}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 rounded-full flex-shrink-0 text-white hover:bg-white/20"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDownloadFile();
                }}
                title="Download"
              >
                <Download className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* Image viewer modal */}
        {isImageViewerOpen && (
          <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm flex items-center justify-center animate-in fade-in-0 duration-200">
            <div
              className={cn(
                "bg-background rounded-lg shadow-xl flex flex-col relative",
                isFullscreen
                  ? "w-full h-full rounded-none"
                  : "max-w-5xl max-h-[90vh] w-full"
              )}
            >
              {/* Header with controls */}
              <div className="flex items-center justify-between p-3 border-b bg-muted/30">
                <div className="flex items-center gap-2">
                  <ImageIcon className="h-4 w-4 text-muted-foreground" />
                  <p className="text-sm font-medium truncate max-w-[200px]">
                    {imageFileName}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 gap-1 text-xs"
                    onClick={handleZoomOut}
                    title="Zoom out"
                  >
                    <ZoomOut className="h-3.5 w-3.5" />
                    <span>Zoom Out</span>
                  </Button>
                  <span className="text-xs font-medium bg-muted px-2 py-1 rounded">
                    {Math.round(zoomLevel * 100)}%
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 gap-1 text-xs"
                    onClick={handleZoomIn}
                    title="Zoom in"
                  >
                    <ZoomIn className="h-3.5 w-3.5" />
                    <span>Zoom In</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 gap-1 text-xs"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDownloadFile();
                    }}
                    title="Download"
                  >
                    <Download className="h-3.5 w-3.5" />
                    <span>Download</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 gap-1 text-xs"
                    onClick={handleFullscreenToggle}
                    title="Toggle fullscreen"
                  >
                    {isFullscreen ? (
                      <Minimize className="h-3.5 w-3.5" />
                    ) : (
                      <Maximize className="h-3.5 w-3.5" />
                    )}
                    <span>{isFullscreen ? "Exit Full" : "Fullscreen"}</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={handleCloseImageViewer}
                    title="Close"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Image container */}
              <div className="flex-1 overflow-auto p-4 flex items-center justify-center bg-black/5">
                <img
                  src={message.fileUrl}
                  alt={message.content || "Image"}
                  className="max-h-full object-contain transition-transform duration-300 shadow-lg"
                  style={{ transform: `scale(${zoomLevel})` }}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }, [
    message.fileUrl,
    message.content,
    message.timestamp,
    fileExt,
    isOwn,
    imageLoaded,
    isImageViewerOpen,
    isFullscreen,
    zoomLevel,
    handleImageClick,
    handleZoomOut,
    handleZoomIn,
    handleDownloadFile,
    handleFullscreenToggle,
    handleCloseImageViewer,
  ]);

  // Check if it's an audio file
  const isAudioFile = useMemo(() => {
    return (
      message.type === "file" && message.fileUrl?.match(/\.(mp3|wav|ogg)$/i)
    );
  }, [message.type, message.fileUrl]);

  // Handle audio file messages
  const renderAudioContent = useCallback(() => {
      if (!message.fileUrl) return null;

      const audioFileName = message.content || `Audio.${fileExt}`;
      const timestamp = format(new Date(message.timestamp), "HH:mm");

      return (
        <div className="mt-1">
          <div
            className={cn(
              "p-3 rounded-lg shadow-md hover:shadow-lg transition-shadow",
              isOwn
                ? "bg-gradient-to-br from-primary to-primary/90 text-primary-foreground"
                : "bg-gradient-to-br from-muted to-muted/90"
            )}
          >
            <div className="flex items-center gap-3">
              <div className="flex-shrink-0 h-10 w-10 rounded-full bg-primary-foreground/20 flex items-center justify-center">
                <FileIcon className="h-5 w-5 text-primary-foreground" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium truncate">
                  {audioFileName}
                </div>
                <div className="text-xs text-primary-foreground/70">
                  Audio file • {fileExt.toUpperCase()}
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 rounded-full flex-shrink-0 text-primary-foreground hover:bg-primary-foreground/10"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDownloadFile();
                }}
                title="Download"
              >
                <Download className="h-4 w-4" />
              </Button>
            </div>

            {/* Audio preview with animated waveform-like visual */}
            <div className="mt-3 bg-primary-foreground/10 backdrop-blur-sm rounded-lg p-3">
              <div className="flex items-center justify-center mb-3">
                <div className="flex items-end h-12 gap-[2px]">
                  {Array.from({ length: 40 }).map((_, i) => {
                    // Create a visual waveform effect with animation
                    const height = Math.floor(Math.random() * 100) % 100;
                    return (
                      <div
                        key={i}
                        className="w-[3px] bg-primary-foreground/60 animate-pulse"
                        style={{
                          height: `${Math.max(15, height)}%`,
                          animationDelay: `${i * 0.05}s`,
                          opacity: i % 2 === 0 ? 0.7 : 0.5,
                        }}
                      />
                    );
                  })}
                </div>
              </div>
              <audio
                controls
                className="max-w-full w-full rounded-md"
                controlsList="nodownload"
              >
                <source src={message.fileUrl} type={`audio/${fileExt}`} />
                Your browser does not support the audio element.
              </audio>
            </div>

            <div className="flex justify-end mt-2">
              <span className="text-xs text-primary-foreground/70">
                {timestamp}
              </span>
            </div>
          </div>
        </div>
      );
    }, [message.fileUrl, message.content, fileExt, isOwn, handleDownloadFile]);

  // Handle generic file messages
  const renderFileContent = useCallback(() => {
      if (!message.fileUrl) return null;

      // Get file type info
      const fileTypeInfo = getFileTypeInfo(fileExt);

      // Extract file size from the message content if available
      // Format: "filename.ext (size)" or just use a consistent size based on file type
      let fileSizeFormatted = "";

      // Try to extract size from content if it's in the format "filename (size)"
      const sizeMatch = message.content?.match(/\(([0-9.]+)\s*(KB|MB|GB)\)/i);
      if (sizeMatch && sizeMatch[1] && sizeMatch[2]) {
        fileSizeFormatted = `${sizeMatch[1]} ${sizeMatch[2].toUpperCase()}`;
      } else {
        // Otherwise, use a consistent size based on file extension
        let fileSizeKB = 0;

        if (fileExt === "pdf") {
          fileSizeKB = 3660; // ~3.6 MB for PDF
        } else if (fileExt === "doc" || fileExt === "docx") {
          fileSizeKB = 2450; // ~2.4 MB for Word docs
        } else if (fileExt === "xls" || fileExt === "xlsx") {
          fileSizeKB = 1800; // ~1.8 MB for Excel
        } else if (fileExt === "ppt" || fileExt === "pptx") {
          fileSizeKB = 4200; // ~4.2 MB for PowerPoint
        } else {
          // Default size for other file types
          fileSizeKB = 1500; // ~1.5 MB
        }

        fileSizeFormatted = fileSizeKB >= 1024
          ? `${(fileSizeKB / 1024).toFixed(2)} MB`
          : `${fileSizeKB} KB`;
      }

      return (
        <div className="mt-1">
          <div
            className={cn(
              "p-3 rounded-lg transition-shadow",
              isOwn
                ? "bg-[hsl(var(--chat-bubble-own))] text-[hsl(var(--chat-bubble-own-text))]"
                : "bg-[hsl(var(--chat-bubble-other))] text-[hsl(var(--chat-bubble-other-text))]"
            )}
          >
            {/* File preview card - styled like the reference image */}
            <div className="bg-card rounded-md overflow-hidden border shadow-sm">
              {/* File thumbnail/preview area - styled to match the reference image */}
              <div className="flex items-stretch">
                <div
                  className={cn(
                    "w-20 flex-shrink-0 flex items-center justify-center py-3",
                    fileTypeInfo.color || "bg-primary/10"
                  )}
                >
                  {fileTypeInfo.icon}
                </div>

                {/* File info */}
                <div className="flex-1 p-3 flex flex-col justify-between min-w-0">
                  <div>
                    <div className="text-sm font-medium truncate text-card-foreground">
                      {fileName}
                    </div>
                    <div className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                      <span>
                        {fileSizeFormatted}
                        {fileExt && (
                          <>
                            {", "}
                            {fileExt.toLowerCase() === "pdf"
                              ? "PDF Document"
                              : fileExt.toLowerCase() === "doc" || fileExt.toLowerCase() === "docx"
                                ? "Microsoft Word Document"
                                : fileExt.toLowerCase() === "xls" || fileExt.toLowerCase() === "xlsx"
                                  ? "Microsoft Excel Spreadsheet"
                                  : fileExt.toLowerCase() === "ppt" || fileExt.toLowerCase() === "pptx"
                                    ? "Microsoft PowerPoint Presentation"
                                    : fileTypeInfo.label}
                          </>
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action buttons */}
              <div className="flex border-t">
                {/* For Office documents, we'll use a button that triggers download with a different message */}
                {(fileExt.toLowerCase() === "doc" || fileExt.toLowerCase() === "docx" ||
                  fileExt.toLowerCase() === "xls" || fileExt.toLowerCase() === "xlsx" ||
                  fileExt.toLowerCase() === "ppt" || fileExt.toLowerCase() === "pptx") ? (
                  <button
                    className="flex-1 text-center py-2 text-sm hover:bg-muted transition-colors text-primary"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Show a toast about opening in the appropriate Office app
                      let appName = "Microsoft Office";
                      if (fileExt.toLowerCase() === "doc" || fileExt.toLowerCase() === "docx") {
                        appName = "Microsoft Word";
                      } else if (fileExt.toLowerCase() === "xls" || fileExt.toLowerCase() === "xlsx") {
                        appName = "Microsoft Excel";
                      } else if (fileExt.toLowerCase() === "ppt" || fileExt.toLowerCase() === "pptx") {
                        appName = "Microsoft PowerPoint";
                      }

                      toast({
                        title: "Opening document",
                        description: `The document will open in ${appName} if installed.`,
                      });
                      // Then trigger the download
                      handleDownloadFile();
                    }}
                  >
                    Open
                  </button>
                ) : (
                  <a
                    href={message.fileUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 text-center py-2 text-sm hover:bg-muted transition-colors text-primary"
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  >
                    Open
                  </a>
                )}
                <div className="w-px bg-border"></div>
                <button
                  className="flex-1 text-center py-2 text-sm hover:bg-muted transition-colors text-primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDownloadFile();
                  }}
                >
                  Save as...
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    }, [
      message.fileUrl,
      fileName,
      fileExt,
      isOwn,
      handleDownloadFile,
      getFileTypeInfo,
      toast,
    ]);

  // Render text message content
  const renderTextContent = useCallback(() => {
      return (
        <div className="text-sm whitespace-pre-wrap leading-relaxed">
          {/* Auto-linkify URLs */}
          {message.content.split(/\s+/).map((word, i, arr) => {
            // Check if the word is a URL
            const isUrl = /^(https?:\/\/[^\s]+)$/.test(word);

            if (isUrl) {
              return (
                <React.Fragment key={i}>
                  <a
                    href={word}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={cn(
                      "underline underline-offset-2 transition-colors font-medium",
                      isOwn
                        ? "text-[hsl(var(--chat-bubble-own-text))] hover:text-[hsl(var(--chat-bubble-own-text))/90]"
                        : "text-primary hover:text-primary/90"
                    )}
                  >
                    {word}
                  </a>
                  {i < arr.length - 1 ? " " : ""}
                </React.Fragment>
              );
            }

            return (
              <React.Fragment key={i}>
                {word}
                {i < arr.length - 1 ? " " : ""}
              </React.Fragment>
            );
          })}
        </div>
      );
    }, [message.content, isOwn]);

  // Render content based on message type
  const renderContent = () => {
    // Handle image type messages directly
    if (isImageMessage) {
      return renderImageContent();
    } else if (isAudioFile) {
      return renderAudioContent();
    } else if (message.type === "file" && message.fileUrl) {
      return renderFileContent();
    } else {
      return renderTextContent();
    }
  };







  // Get bubble shape based on position in group
  const getBubbleShape = useMemo(() => {
    if (isFirstInGroup && isLastInGroup) {
      return isOwn
        ? "rounded-tl-lg rounded-bl-lg rounded-tr-lg rounded-br-sm"
        : "rounded-tr-lg rounded-br-lg rounded-tl-lg rounded-bl-sm";
    } else if (isFirstInGroup && !isLastInGroup) {
      return isOwn
        ? "rounded-tl-lg rounded-bl-lg rounded-tr-lg"
        : "rounded-tr-lg rounded-br-sm rounded-tl-lg";
    } else if (!isFirstInGroup && isLastInGroup) {
      return isOwn
        ? "rounded-tl-lg rounded-bl-lg rounded-br-sm"
        : "rounded-tr-lg rounded-br-lg rounded-bl-sm";
    } else {
      return isOwn
        ? "rounded-tl-lg rounded-bl-lg"
        : "rounded-tr-lg rounded-br-sm";
    }
  }, [isFirstInGroup, isLastInGroup, isOwn]);

  return (
    <div
      className={cn(
        "flex items-end gap-3 group",
        isOwn ? "flex-row-reverse" : "flex-row",
        !isLastInGroup ? "mb-1" : "mb-3"
      )}
      ref={messageRef}
    >
      {showAvatar && !isOwn ? (
        <Avatar className="w-8 h-8 transition-transform hover:scale-105 shadow-sm">
          <AvatarImage
            src={sender?.profileImage}
            alt={sender?.name || "User"}
          />
          <AvatarFallback className="bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] text-sm font-medium">
            {sender?.name?.[0]?.toUpperCase() || "?"}
          </AvatarFallback>
        </Avatar>
      ) : (
        <div className="w-8" />
      )}
      <div
        className={cn(
          "chat-bubble relative transition-all duration-200 hover:shadow-md",
          isOwn ? "chat-bubble-own" : "chat-bubble-other",
          getBubbleShape
        )}
        onMouseEnter={() => setShowActions(true)}
        onMouseLeave={() => setShowActions(false)}
      >
        {renderContent()}

        {/* Message actions */}
        <div
          className={cn(
            "absolute top-0 right-0 transform translate-x-1 -translate-y-1/2 z-10",
            isOwn ? "left-0 right-auto -translate-x-1" : "",
            showActions
              ? "opacity-100 scale-100"
              : "opacity-0 scale-95 group-hover:opacity-100 group-hover:scale-100",
            "transition-all duration-200"
          )}
        >
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 rounded-full bg-background shadow-sm hover:bg-background/90"
                aria-label="Message actions"
              >
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align={isOwn ? "start" : "end"}
              className="w-40 animate-in slide-in-from-top-5 zoom-in-95"
            >
              <DropdownMenuItem
                onClick={handleReply}
                className="cursor-pointer"
              >
                <Reply className="h-4 w-4 mr-2" />
                Reply
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleCopy} className="cursor-pointer">
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </DropdownMenuItem>
              {isOwn && (
                <DropdownMenuItem
                  onClick={handleDelete}
                  className="text-destructive cursor-pointer"
                >
                  <Trash className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>



        <div className="flex justify-end mt-1">
          <MessageListStatus
            status={status as any}
            timestamp={new Date(message.timestamp)}
            isOwn={isOwn}
          />
        </div>
      </div>
    </div>
  );
}

/**
 * Skeleton loader for message component
 */
export function MessageSkeleton() {
  return (
    <div className="flex items-end gap-2 mb-4">
      <Skeleton className="h-6 w-6 rounded-full" />
      <Skeleton className="h-10 w-[200px] rounded-2xl" />
    </div>
  );
}
