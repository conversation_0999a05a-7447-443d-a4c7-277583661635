# Jenkins Pipeline Setup for Thesis Management System

This document provides comprehensive instructions for setting up and configuring the Jenkins pipeline for the Thesis Management System (TMS) deployment.

## Overview

The Jenkins pipeline automates the complete CI/CD process for the TMS application, including:
- Source code retrieval from GitHub
- Docker-based application building
- Automated testing and security scanning
- Multi-environment deployment (development/production)
- Health checks and verification
- Rollback capabilities
- Notifications and reporting

## Prerequisites

### Jenkins Server Requirements
- Jenkins 2.400+ with Pipeline plugin
- Docker and Docker Compose installed on Jenkins server
- Git plugin for source code management
- Required Jenkins plugins:
  - Pipeline
  - Git
  - Docker Pipeline
  - Slack Notification (optional)
  - Email Extension
  - Build User Vars (optional)

### System Requirements
- Linux-based Jenkins server (Ubuntu 20.04+ recommended)
- Minimum 4GB RAM, 2 CPU cores
- 50GB+ disk space for Docker images and builds
- Network access to GitHub and Docker Hub

## Jenkins Configuration

### 1. Install Required Plugins

Navigate to **Manage Jenkins > Manage Plugins** and install:

```
- Pipeline
- Git
- Docker Pipeline
- Docker Commons
- Slack Notification Plugin
- Email Extension Plugin
- Build User Vars Plugin
- Timestamper
- AnsiColor
```

### 2. Configure Global Tools

Go to **Manage Jenkins > Global Tool Configuration**:

#### Git Configuration
- Name: `Default`
- Path to Git executable: `/usr/bin/git`

#### Docker Configuration
- Ensure Docker is installed and Jenkins user has access to Docker daemon

### 3. Set Up Credentials

Navigate to **Manage Jenkins > Manage Credentials > System > Global credentials**:

#### Required Credentials:

1. **GitHub Access** (`github-credentials`)
   - Type: Username with password or SSH Username with private key
   - ID: `github-credentials`
   - Description: GitHub repository access

2. **Docker Registry** (`docker-hub-credentials`)
   - Type: Username with password
   - ID: `docker-hub-credentials`
   - Description: Docker Hub or private registry access

3. **Deployment Server** (`deploy-host`, `deploy-user`, `deploy-ssh-key`)
   - `deploy-host`: Secret text with server hostname/IP
   - `deploy-user`: Secret text with deployment username
   - `deploy-ssh-key`: SSH Username with private key

4. **Application Environment Variables**
   - `appwrite-project-id`: Secret text
   - `appwrite-database-id`: Secret text
   - `appwrite-openssl-key`: Secret text (optional, auto-generated if not provided)
   - `appwrite-executor-secret`: Secret text (optional, auto-generated if not provided)

5. **Notification Settings** (Optional)
   - `slack-token`: Secret text for Slack notifications
   - Email SMTP configuration in Jenkins system settings

## Pipeline Setup

### 1. Create New Pipeline Job

1. Go to Jenkins dashboard
2. Click **New Item**
3. Enter job name: `TMS-Deployment`
4. Select **Pipeline**
5. Click **OK**

### 2. Configure Pipeline

#### General Settings
- ✅ **This project is parameterized** (parameters are defined in Jenkinsfile)
- ✅ **Do not allow concurrent builds**

#### Build Triggers
Configure as needed:
- **GitHub hook trigger for GITScm polling** (for automatic builds)
- **Poll SCM**: `H/5 * * * *` (check every 5 minutes)

#### Pipeline Configuration
- **Definition**: Pipeline script from SCM
- **SCM**: Git
- **Repository URL**: `https://github.com/your-username/thesis-management-system.git`
- **Credentials**: Select your GitHub credentials
- **Branch Specifier**: `*/main`
- **Script Path**: `Jenkinsfile`

### 3. Update Repository URL

Edit the `Jenkinsfile` and update line 78:
```groovy
url: 'https://github.com/your-username/thesis-management-system.git'
```

## Environment Configuration

### 1. Application Environment Variables

The pipeline automatically creates a `.env` file during deployment. You can customize these values by adding them as Jenkins credentials:

```bash
# Core Application
NODE_ENV=production
ENVIRONMENT=production

# Appwrite Configuration
APPWRITE_PROJECT_ID=your-project-id
APPWRITE_DATABASE_ID=your-database-id

# Security (auto-generated if not provided)
_APP_OPENSSL_KEY_V1=auto-generated-key
_APP_EXECUTOR_SECRET=auto-generated-secret

# Database
_APP_DB_USER=appwrite
_APP_DB_PASS=auto-generated-password
_APP_DB_ROOT_PASS=auto-generated-password

# Domain (update for production)
_APP_DOMAIN=http://localhost
_APP_DOMAIN_TARGET=http://localhost
DOMAIN=localhost

# Email Configuration
_APP_SYSTEM_EMAIL_NAME=TMS System
_APP_SYSTEM_EMAIL_ADDRESS=noreply@localhost
_APP_SYSTEM_SECURITY_EMAIL_ADDRESS=security@localhost

# SMTP (optional)
_APP_SMTP_HOST=
_APP_SMTP_PORT=587
_APP_SMTP_SECURE=tls
_APP_SMTP_USERNAME=
_APP_SMTP_PASSWORD=

# SSL (for production)
SSL_EMAIL=admin@localhost
```

### 2. Production Configuration

For production deployments, update these values:

```bash
# Production Domain
_APP_DOMAIN=https://your-domain.com
_APP_DOMAIN_TARGET=https://your-domain.com
DOMAIN=your-domain.com

# Production Email
_APP_SYSTEM_EMAIL_ADDRESS=<EMAIL>
_APP_SYSTEM_SECURITY_EMAIL_ADDRESS=<EMAIL>
SSL_EMAIL=<EMAIL>

# SMTP Configuration
_APP_SMTP_HOST=smtp.your-provider.com
_APP_SMTP_PORT=587
_APP_SMTP_SECURE=tls
_APP_SMTP_USERNAME=your-smtp-username
_APP_SMTP_PASSWORD=your-smtp-password
```

## Usage

### Running the Pipeline

1. Go to your Jenkins job
2. Click **Build with Parameters**
3. Configure parameters:
   - **ENVIRONMENT**: `development` or `production`
   - **DEPLOY_ACTION**: `deploy`, `rollback`, or `stop`
   - **GIT_BRANCH**: Branch to deploy (default: `main`)
   - **RUN_TESTS**: Enable/disable testing
   - **FORCE_REBUILD**: Force Docker image rebuild

### Pipeline Parameters

| Parameter | Description | Options |
|-----------|-------------|---------|
| ENVIRONMENT | Target deployment environment | development, production |
| DEPLOY_ACTION | Action to perform | deploy, rollback, stop |
| GIT_BRANCH | Git branch to deploy | Any branch name |
| RUN_TESTS | Run tests before deployment | true, false |
| FORCE_REBUILD | Force rebuild Docker images | true, false |

### Deployment Scenarios

#### 1. Development Deployment
```
ENVIRONMENT: development
DEPLOY_ACTION: deploy
GIT_BRANCH: main
RUN_TESTS: true
FORCE_REBUILD: false
```

#### 2. Production Deployment
```
ENVIRONMENT: production
DEPLOY_ACTION: deploy
GIT_BRANCH: main
RUN_TESTS: true
FORCE_REBUILD: true
```

#### 3. Emergency Rollback
```
ENVIRONMENT: production
DEPLOY_ACTION: rollback
GIT_BRANCH: main
RUN_TESTS: false
FORCE_REBUILD: false
```

#### 4. Stop Services
```
ENVIRONMENT: production
DEPLOY_ACTION: stop
GIT_BRANCH: main
RUN_TESTS: false
FORCE_REBUILD: false
```

## Monitoring and Troubleshooting

### Build Logs
- Access build logs through Jenkins job console output
- Logs are automatically archived for failed builds

### Health Checks
The pipeline includes automatic health checks:
- Container status verification
- Application endpoint testing
- Service dependency validation

### Notifications
- **Success**: Slack and email notifications with deployment details
- **Failure**: Detailed error notifications with build logs
- **Automatic Rollback**: Production failures trigger automatic rollback

### Common Issues

1. **Docker Permission Denied**
   ```bash
   sudo usermod -aG docker jenkins
   sudo systemctl restart jenkins
   ```

2. **Git Authentication Failed**
   - Verify GitHub credentials in Jenkins
   - Check repository URL and access permissions

3. **Docker Build Failures**
   - Check Dockerfile syntax
   - Verify base image availability
   - Review build logs for specific errors

4. **Health Check Failures**
   - Verify application startup time
   - Check port configurations
   - Review application logs

### Logs and Artifacts
- Build reports: `build-report.txt`
- Deployment info: `deployment-info.json`
- Docker logs: Available through `docker-compose logs`

## Security Considerations

1. **Credentials Management**
   - Store all sensitive data in Jenkins credentials
   - Use least-privilege access for service accounts
   - Regularly rotate secrets and keys

2. **Network Security**
   - Restrict Jenkins server access
   - Use HTTPS for all external communications
   - Configure firewall rules appropriately

3. **Container Security**
   - Regular security scans with Trivy (if available)
   - Keep base images updated
   - Monitor for vulnerabilities

## Maintenance

### Regular Tasks
1. **Weekly**: Review build logs and performance metrics
2. **Monthly**: Update Jenkins plugins and Docker images
3. **Quarterly**: Review and update security configurations
4. **As needed**: Clean up old Docker images and build artifacts

### Backup Strategy
- Jenkins configuration backup
- Docker volume backups
- Database backups (handled by application deployment script)

## Support

For issues and questions:
1. Check Jenkins build logs
2. Review application logs: `docker-compose logs`
3. Verify system resources and dependencies
4. Consult this documentation and application README files

---

**Note**: This pipeline is designed for the Thesis Management System. Customize the configuration according to your specific requirements and infrastructure setup.
