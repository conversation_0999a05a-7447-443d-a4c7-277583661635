'use client';

import { Head<PERSON> } from '@/components/layout/Header';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useCallback, useState } from 'react';
import { useToast } from '@/hooks/useToast';
import { Toaster } from '@/components/ui/toaster';
import { useAuth } from '@/hooks/useAuth';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { User, UserSettings, NotificationSettings, DisplaySettings } from '@/lib/types';
import { updateUser } from '@/lib/api/auth';
import { useTheme } from 'next-themes';
import { useRouter } from 'next/navigation';
import { Bell, Moon, Sun, Shield, Eye, KeyIcon } from 'lucide-react';

// Default settings
const defaultSettings: UserSettings = {
  notifications: {
    enableDesktopNotifications: true,
    emailNotifications: true,
    mutedTypes: [],
  },
  display: {
    compactView: false,
    highContrastMode: false,
    largeText: false,
  },
};

export default function SettingsPage() {
  const { user, isLoading } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { theme, setTheme } = useTheme();
  const router = useRouter();

  // Initialize settings with defaults or user settings if available
  const [settings, setSettings] = useState<UserSettings>(
    user?.settings || defaultSettings
  );

  // Update settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: async (updates: Partial<User>) => {
      if (!user?.id) throw new Error('No user ID');
      return await updateUser(user.id, updates);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['auth', 'user'] });
      toast({
        title: 'Settings updated',
        description: 'Your settings have been updated successfully.',
      });
    },
    onError: (error) => {
      console.error('Error updating settings:', error);
      toast({
        variant: 'destructive',
        title: 'Update failed',
        description: 'Failed to update your settings. Please try again.',
      });
    },
  });

  // Handle notification settings change
  const handleNotificationChange = (key: keyof NotificationSettings, value: boolean) => {
    setSettings((prev) => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [key]: value,
      },
    }));
  };

  // Handle display settings change
  const handleDisplayChange = (key: keyof DisplaySettings, value: boolean) => {
    setSettings((prev) => ({
      ...prev,
      display: {
        ...prev.display,
        [key]: value,
      },
    }));
  };

  // Save settings
  const saveSettings = useCallback(() => {
    updateSettingsMutation.mutate({
      settings,
    });
  }, [settings, updateSettingsMutation]);

  // Navigate to change password page
  const navigateToChangePassword = () => {
    router.push('/auth/change-password');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (!user) {
    return <div>Not authorized</div>;
  }

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <Toaster />
      <Header />
      <main className="flex-1 flex items-center justify-center py-6 px-4">
        <div className="w-full max-w-2xl space-y-6">
          <div className="text-center">
            <h1 className="text-2xl font-bold tracking-tight">Settings</h1>
            <p className="text-sm text-muted-foreground">
              Manage your application preferences and settings
            </p>
          </div>

          <Tabs defaultValue="appearance" className="w-full">
            <TabsList className="grid grid-cols-4 mb-4">
              <TabsTrigger value="appearance">
                <div className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  <span className="hidden sm:inline">Appearance</span>
                </div>
              </TabsTrigger>
              <TabsTrigger value="notifications">
                <div className="flex items-center gap-2">
                  <Bell className="h-4 w-4" />
                  <span className="hidden sm:inline">Notifications</span>
                </div>
              </TabsTrigger>
              <TabsTrigger value="display">
                <div className="flex items-center gap-2">
                  <Sun className="h-4 w-4" />
                  <span className="hidden sm:inline">Display</span>
                </div>
              </TabsTrigger>
              <TabsTrigger value="security">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  <span className="hidden sm:inline">Security</span>
                </div>
              </TabsTrigger>
            </TabsList>

            {/* Appearance Tab */}
            <TabsContent value="appearance">
              <Card>
                <CardHeader>
                  <CardTitle>Appearance</CardTitle>
                  <CardDescription>
                    Customize how the application looks
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium">Theme</h3>
                      <p className="text-sm text-muted-foreground">
                        Select your preferred theme
                      </p>
                    </div>
                    <div className="flex flex-col gap-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Sun className="h-5 w-5" />
                          <Label htmlFor="theme-light">Light</Label>
                        </div>
                        <Switch
                          id="theme-light"
                          checked={theme === 'light'}
                          onCheckedChange={() => setTheme('light')}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Moon className="h-5 w-5" />
                          <Label htmlFor="theme-dark">Dark</Label>
                        </div>
                        <Switch
                          id="theme-dark"
                          checked={theme === 'dark'}
                          onCheckedChange={() => setTheme('dark')}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Sun className="h-5 w-5 opacity-50" />
                          <Label htmlFor="theme-system">System</Label>
                        </div>
                        <Switch
                          id="theme-system"
                          checked={theme === 'system'}
                          onCheckedChange={() => setTheme('system')}
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Notifications Tab */}
            <TabsContent value="notifications">
              <Card>
                <CardHeader>
                  <CardTitle>Notifications</CardTitle>
                  <CardDescription>
                    Manage how you receive notifications
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="desktop-notifications">Desktop Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Show notifications on your desktop
                      </p>
                    </div>
                    <Switch
                      id="desktop-notifications"
                      checked={settings.notifications.enableDesktopNotifications}
                      onCheckedChange={(checked) =>
                        handleNotificationChange('enableDesktopNotifications', checked)
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="email-notifications">Email Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Receive notifications via email
                      </p>
                    </div>
                    <Switch
                      id="email-notifications"
                      checked={settings.notifications.emailNotifications}
                      onCheckedChange={(checked) =>
                        handleNotificationChange('emailNotifications', checked)
                      }
                    />
                  </div>
                  <div className="pt-4">
                    <Button onClick={saveSettings}>Save Notification Settings</Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Display Tab */}
            <TabsContent value="display">
              <Card>
                <CardHeader>
                  <CardTitle>Display</CardTitle>
                  <CardDescription>
                    Customize your display preferences
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="compact-view">Compact View</Label>
                      <p className="text-sm text-muted-foreground">
                        Use a more compact layout
                      </p>
                    </div>
                    <Switch
                      id="compact-view"
                      checked={settings.display.compactView}
                      onCheckedChange={(checked) =>
                        handleDisplayChange('compactView', checked)
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="high-contrast">High Contrast Mode</Label>
                      <p className="text-sm text-muted-foreground">
                        Increase contrast for better visibility
                      </p>
                    </div>
                    <Switch
                      id="high-contrast"
                      checked={settings.display.highContrastMode}
                      onCheckedChange={(checked) =>
                        handleDisplayChange('highContrastMode', checked)
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="large-text">Large Text</Label>
                      <p className="text-sm text-muted-foreground">
                        Use larger text throughout the application
                      </p>
                    </div>
                    <Switch
                      id="large-text"
                      checked={settings.display.largeText}
                      onCheckedChange={(checked) =>
                        handleDisplayChange('largeText', checked)
                      }
                    />
                  </div>
                  <div className="pt-4">
                    <Button onClick={saveSettings}>Save Display Settings</Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Security Tab */}
            <TabsContent value="security">
              <Card>
                <CardHeader>
                  <CardTitle>Security</CardTitle>
                  <CardDescription>
                    Manage your account security settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium">Password</h3>
                      <p className="text-sm text-muted-foreground">
                        Change your password to keep your account secure
                      </p>
                    </div>
                    <Button
                      onClick={navigateToChangePassword}
                      className="flex items-center gap-2"
                    >
                      <KeyIcon className="h-4 w-4" />
                      Change Password
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
}
