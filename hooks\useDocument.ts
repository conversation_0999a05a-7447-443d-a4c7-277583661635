import { useMutation, useQuery, useQueryClient, type UseQueryOptions } from '@tanstack/react-query';
import type { Document, DocumentStatus, DocumentType } from '@/lib/types';
import {
  createDocument,
  getDocument,
  getDocuments,
  getDocumentsByStudentId,
  getDocumentsByStatus,
  getDocumentsBySupervisorId,
  updateDocument,
  deleteDocument,
  uploadDocument,
} from '@/lib/api/documents';
import { withRetry } from '@/lib/utils';

// Constants
const STALE_TIME = 5 * 60 * 1000; // 5 minutes

// Query Keys
export const DOCUMENT_QUERY_KEYS = {
  all: ['documents'] as const,
  lists: () => [...DOCUMENT_QUERY_KEYS.all, 'list'] as const,
  list: (filters?: DocumentFilters) => [...DOCUMENT_QUERY_KEYS.lists(), filters] as const,
  details: () => [...DOCUMENT_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...DOCUMENT_QUERY_KEYS.details(), id] as const,
  versions: (id: string) => [...DOCUMENT_QUERY_KEYS.all, 'versions', id] as const,
  project: (projectId: string) => [...DOCUMENT_QUERY_KEYS.all, 'project', projectId] as const,
  student: (studentId: string) => [...DOCUMENT_QUERY_KEYS.all, 'student', studentId] as const,
  supervisor: (supervisorId: string) => [...DOCUMENT_QUERY_KEYS.all, 'supervisor', supervisorId] as const,
  status: (status: DocumentStatus) => [...DOCUMENT_QUERY_KEYS.all, 'status', status] as const,
} as const;

// Types
export interface DocumentFilters {
  projectId?: string;
  studentId?: string;
  supervisorId?: string;
  status?: DocumentStatus;
  type?: DocumentType;
}

export interface CreateDocumentParams {
  document: Omit<Document, "id" | "createdAt" | "updatedAt">;
}

export interface UpdateDocumentParams {
  id: string;
  feedback?: string;
  status?: DocumentStatus;
  createVersion?: boolean;
  changeDescription?: string;
  userId?: string;
  title?: string;
  content?: string;
  type?: DocumentType;
  supervisorIds?: string[];
  reviewDue?: Date;
}

export interface UploadDocumentParams {
  file: File;
  metadata: {
    type: DocumentType;
    title: string;
    projectId: string;
    studentId: string;
    supervisorIds: string[];
    status?: DocumentStatus;
    content?: string;
  };
  onProgress?: (progress: number) => void;
}

export interface DocumentQueryOptions extends Omit<UseQueryOptions<Document[], Error>, 'queryKey' | 'queryFn'> {
  queryKey?: string[];
}

/**
 * Custom hook for document operations with improved type safety and error handling
 */
export function useDocument() {
  const queryClient = useQueryClient();

  /**
   * Invalidates all document-related queries
   */
  const invalidateDocumentQueries = (documentId?: string) => {
    queryClient.invalidateQueries({ queryKey: DOCUMENT_QUERY_KEYS.all });
    if (documentId) {
      queryClient.invalidateQueries({ queryKey: DOCUMENT_QUERY_KEYS.detail(documentId) });
      queryClient.invalidateQueries({ queryKey: DOCUMENT_QUERY_KEYS.versions(documentId) });
    }
  };

  /**
   * Create a new document
   */
  const createDocumentMutation = useMutation({
    mutationFn: (params: CreateDocumentParams) => createDocument(params.document),
    onSuccess: (newDocument: Document) => {
      // Optimistic update - add to cache immediately
      queryClient.setQueryData(
        DOCUMENT_QUERY_KEYS.detail(newDocument.id),
        newDocument
      );

      // Invalidate list queries to refetch with new document
      queryClient.invalidateQueries({ queryKey: DOCUMENT_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: DOCUMENT_QUERY_KEYS.project(newDocument.projectId) });
      queryClient.invalidateQueries({ queryKey: DOCUMENT_QUERY_KEYS.student(newDocument.studentId) });
    },
    onError: (error) => {
      console.error('Failed to create document:', error);
    },
  });

  /**
   * Update an existing document
   */
  const updateDocumentMutation = useMutation({
    mutationFn: (params: UpdateDocumentParams) => {
      const { id, ...updates } = params;
      return updateDocument(id, updates, {
        notifyParticipants: true,
        updateStatus: true,
      });
    },
    onMutate: async (params: UpdateDocumentParams) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: DOCUMENT_QUERY_KEYS.detail(params.id) });

      // Snapshot previous value
      const previousDocument = queryClient.getQueryData<Document>(
        DOCUMENT_QUERY_KEYS.detail(params.id)
      );

      // Optimistically update
      if (previousDocument) {
        const optimisticDocument = { ...previousDocument, ...params };
        queryClient.setQueryData(
          DOCUMENT_QUERY_KEYS.detail(params.id),
          optimisticDocument
        );
      }

      return { previousDocument };
    },
    onError: (error, params, context) => {
      // Rollback on error
      if (context?.previousDocument) {
        queryClient.setQueryData(
          DOCUMENT_QUERY_KEYS.detail(params.id),
          context.previousDocument
        );
      }
      console.error('Failed to update document:', error);
    },
    onSuccess: (updatedDoc: Document) => {
      // Update cache with server response
      queryClient.setQueryData(
        DOCUMENT_QUERY_KEYS.detail(updatedDoc.id),
        updatedDoc
      );

      // Invalidate related queries
      invalidateDocumentQueries(updatedDoc.id);
      queryClient.invalidateQueries({ queryKey: DOCUMENT_QUERY_KEYS.project(updatedDoc.projectId) });
      queryClient.invalidateQueries({ queryKey: DOCUMENT_QUERY_KEYS.student(updatedDoc.studentId) });
    },
  });

  /**
   * Delete a document
   */
  const deleteDocumentMutation = useMutation({
    mutationFn: deleteDocument,
    onMutate: async (documentId: string) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: DOCUMENT_QUERY_KEYS.detail(documentId) });

      // Get document data before deletion for rollback
      const previousDocument = queryClient.getQueryData<Document>(
        DOCUMENT_QUERY_KEYS.detail(documentId)
      );

      // Optimistically remove from cache
      queryClient.removeQueries({ queryKey: DOCUMENT_QUERY_KEYS.detail(documentId) });

      return { previousDocument };
    },
    onError: (error, documentId, context) => {
      // Rollback on error
      if (context?.previousDocument) {
        queryClient.setQueryData(
          DOCUMENT_QUERY_KEYS.detail(documentId),
          context.previousDocument
        );
      }
      console.error('Failed to delete document:', error);
    },
    onSuccess: (_, documentId) => {
      // Remove from all caches
      queryClient.removeQueries({ queryKey: DOCUMENT_QUERY_KEYS.detail(documentId) });
      queryClient.removeQueries({ queryKey: DOCUMENT_QUERY_KEYS.versions(documentId) });

      // Invalidate list queries
      invalidateDocumentQueries();
    },
  });

  /**
   * Upload a document with file
   */
  const uploadDocumentMutation = useMutation({
    mutationFn: (params: UploadDocumentParams) => {
      return uploadDocument(params.file, params.metadata, params.onProgress);
    },
    onSuccess: (newDocument: Document) => {
      // Add to cache
      queryClient.setQueryData(
        DOCUMENT_QUERY_KEYS.detail(newDocument.id),
        newDocument
      );

      // Invalidate list queries
      queryClient.invalidateQueries({ queryKey: DOCUMENT_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: DOCUMENT_QUERY_KEYS.project(newDocument.projectId) });
      queryClient.invalidateQueries({ queryKey: DOCUMENT_QUERY_KEYS.student(newDocument.studentId) });
    },
    onError: (error) => {
      console.error('Failed to upload document:', error);
    },
  });

  const getDocumentById = (id: string) => {
    return useQuery({
      queryKey: ['documents', 'detail', id],
      queryFn: () => withRetry(() => getDocument(id)),
      enabled: !!id,
      staleTime: STALE_TIME,
    });
  };

  // Create a function that returns query options instead of calling useQuery directly
  const getListQueryOptions = (filters?: DocumentFilters, options: { queryKey?: string[]; enabled?: boolean } = {}) => {
    return {
      queryKey: options.queryKey || ['documents', 'list', filters || 'all'],
      queryFn: () => withRetry(() => {
        // If no filters, get all documents
        if (!filters) {
          return getDocuments();
        }

        // Use the appropriate API function based on the filter
        if (filters.studentId) {
          return getDocumentsByStudentId(filters.studentId);
        } else if (filters.supervisorId) {
          return getDocumentsBySupervisorId(filters.supervisorId);
        } else if (filters.status) {
          return getDocumentsByStatus(filters.status);
        } else if (filters.projectId) {
          // For project documents, we'll use getDocuments since there's no specific function
          return getDocuments();
        }

        // Default fallback
        return getDocuments();
      }),
      staleTime: STALE_TIME,
      ...options,
    };
  };

  // Function that uses useQuery with the options
  const getList = (filters?: DocumentFilters, options: { queryKey?: string[]; enabled?: boolean } = {}) => {
    return useQuery(getListQueryOptions(filters, options));
  };

  return {
    create: createDocumentMutation,
    update: updateDocumentMutation,
    delete: deleteDocumentMutation,
    upload: uploadDocumentMutation,
    getById: getDocumentById,
    getList,
    getListQueryOptions, // Export the query options function for advanced use cases
  };
}

export function useDocumentQueries() {
  const { getList } = useDocument();

  const useProjectDocuments = (projectId: string) =>
    getList(
      projectId ? { projectId } : undefined,
      {
        enabled: !!projectId,
        queryKey: ['documents', 'project', projectId]
      }
    );

  const useStudentDocuments = (studentId: string) =>
    getList(
      studentId ? { studentId } : undefined,
      {
        enabled: !!studentId,
        queryKey: ['documents', 'student', studentId]
      }
    );

  const useSupervisorDocuments = (supervisorId: string) =>
    getList(
      supervisorId ? { supervisorId } : undefined,
      {
        enabled: !!supervisorId,
        queryKey: ['documents', 'supervisor', supervisorId]
      }
    );

  const useDocumentsByStatus = (status: string) =>
    getList(
      status ? { status: status as DocumentStatus } : undefined,
      {
        enabled: !!status,
        queryKey: ['documents', 'status', status]
      }
    );

  return {
    useProjectDocuments,
    useStudentDocuments,
    useSupervisorDocuments,
    useDocumentsByStatus,
  };
}