import * as React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

interface StatsCardProps {
  title: string;
  value: number | string;
  icon?: React.ReactNode;
  description?: string;
  trend?: number;
  loading?: boolean;
  className?: string;
  variant?: "default" | "success" | "warning" | "danger";
}

const StatsCard = React.forwardRef<HTMLDivElement, StatsCardProps>(
  ({ title, value, icon, description, trend, loading, className, variant = "default" }, ref) => {
    const variantStyles = {
      default: "",
      success: "border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/50",
      warning: "border-yellow-200 bg-yellow-50/50 dark:border-yellow-800 dark:bg-yellow-950/50",
      danger: "border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/50",
    };

    const valueStyles = {
      default: "text-card-foreground",
      success: "text-green-700 dark:text-green-300",
      warning: "text-yellow-700 dark:text-yellow-300",
      danger: "text-red-700 dark:text-red-300",
    };

    if (loading) {
      return (
        <Card ref={ref} className={cn(variantStyles[variant], className)}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <Skeleton className="h-4 w-24" />
            {icon && <Skeleton className="h-4 w-4 rounded" />}
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-16 mb-2" />
            {description && <Skeleton className="h-4 w-32" />}
            {trend !== undefined && <Skeleton className="h-4 w-16 mt-1" />}
          </CardContent>
        </Card>
      );
    }

    return (
      <Card ref={ref} className={cn(variantStyles[variant], className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          {icon}
        </CardHeader>
        <CardContent>
          <div className={cn("text-2xl font-bold", valueStyles[variant])}>
            {value}
          </div>
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
          {trend !== undefined && (
            <div className="flex items-center mt-2">
              <span
                className={cn(
                  "text-xs flex items-center font-medium",
                  trend >= 0 ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
                )}
              >
                {trend >= 0 ? "↗" : "↘"} {Math.abs(trend)}%
                <span className="ml-1 text-muted-foreground font-normal">
                  vs last period
                </span>
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }
);

StatsCard.displayName = "StatsCard";

export { StatsCard };
export type { StatsCardProps };
