import { useState, useMemo } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Plus, Trash2 } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { Project, ProjectMilestone } from '@/lib/types';
import { useToast } from '@/hooks/useToast';
import { useMilestones } from '@/hooks/useMilestones';

interface ProjectMilestonesProps {
  project: Project;
  readOnly?: boolean;
}

export function ProjectMilestones({ project, readOnly = false }: ProjectMilestonesProps) {
  const { toast } = useToast();
  const { 
    useMilestonesQuery, 
    createMilestone, 
    updateMilestone, 
    deleteMilestone 
  } = useMilestones(project.id);
  
  const { data: milestones = [], isLoading } = useMilestonesQuery();
  
  const [newMilestone, setNewMilestone] = useState({
    title: '',
    dueDate: new Date(),
  });

  const isAddButtonDisabled = !newMilestone.title.trim();

  const handleAddMilestone = async () => {
    if (isAddButtonDisabled) {
      toast({
        title: "Missing title",
        description: "Please provide a title for the milestone",
        variant: "destructive"
      });
      return;
    }

    createMilestone.mutate({
      projectId: project.id,
      title: newMilestone.title,
      dueDate: newMilestone.dueDate,
      completed: false
    }, {
      onSuccess: () => {
        setNewMilestone({ title: '', dueDate: new Date() });
        toast({
          title: "Milestone added",
          description: "The milestone has been added to the project"
        });
      },
      onError: () => {
        toast({
          title: "Failed to add milestone",
          description: "There was an error adding the milestone",
          variant: "destructive"
        });
      }
    });
  };

  const handleToggleMilestone = (milestoneId: string, completed: boolean) => {
    updateMilestone.mutate({
      id: milestoneId,
      completed,
      completedAt: completed ? new Date() : undefined
    }, {
      onSuccess: () => {
        toast({
          title: `Milestone ${completed ? 'completed' : 'reopened'}`,
          description: `The milestone has been marked as ${completed ? 'completed' : 'not completed'}`
        });
      },
      onError: () => {
        toast({
          title: "Failed to update milestone",
          description: "There was an error updating the milestone status",
          variant: "destructive"
        });
      }
    });
  };

  const handleDeleteMilestone = (milestoneId: string) => {
    deleteMilestone.mutate(milestoneId, {
      onSuccess: () => {
        toast({
          title: "Milestone deleted",
          description: "The milestone has been removed from the project"
        });
      },
      onError: () => {
        toast({
          title: "Failed to delete milestone",
          description: "There was an error deleting the milestone",
          variant: "destructive"
        });
      }
    });
  };

  const sortedMilestones = useMemo(() => 
    [...milestones].sort((a, b) => 
      new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
    ), 
    [milestones]
  );

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Project Milestones</CardTitle>
        {!readOnly && (
          <Button variant="outline" size="sm" onClick={handleAddMilestone} disabled={isAddButtonDisabled}>
            Add Milestone
          </Button>
        )}
      </CardHeader>
      <CardContent>
        {!readOnly && (
          <div className="flex gap-2 mb-6">
            <Input
              placeholder="New milestone title"
              value={newMilestone.title}
              onChange={(e) => setNewMilestone({ ...newMilestone, title: e.target.value })}
              className="flex-1"
            />
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-[240px]">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {newMilestone.dueDate ? format(newMilestone.dueDate, 'PPP') : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={newMilestone.dueDate}
                  onSelect={(date) => date && setNewMilestone({ ...newMilestone, dueDate: date })}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        )}

        <div className="space-y-4">
          {milestones.length === 0 ? (
            <p className="text-muted-foreground text-center py-4">No milestones added yet</p>
          ) : (
            sortedMilestones.map((milestone) => {
              const dueDate = new Date(milestone.dueDate);
              const isOverdue = !milestone.completed && dueDate < new Date();
              
              return (
                <div 
                  key={milestone.id} 
                  className={cn(
                    "flex items-start justify-between p-3 rounded-lg border",
                    milestone.completed ? "bg-muted/50" : "",
                    isOverdue ? "border-red-200" : ""
                  )}
                >
                  <div className="flex items-start gap-3">
                    {!readOnly && (
                      <Checkbox 
                        checked={milestone.completed}
                        onCheckedChange={(checked: boolean) => handleToggleMilestone(milestone.id, !!checked)}
                        className="mt-1"
                      />
                    )}
                    <div>
                      <p className={cn("font-medium", milestone.completed ? "line-through text-muted-foreground" : "")}>
                        {milestone.title}
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        <CalendarIcon className="h-3 w-3 text-muted-foreground" />
                        <span className={cn(
                          "text-xs",
                          isOverdue ? "text-red-500 font-medium" : "text-muted-foreground"
                        )}>
                          {isOverdue ? "Overdue: " : "Due: "}
                          {format(dueDate, 'PPP')}
                        </span>
                      </div>
                      {milestone.completed && milestone.completedAt && (
                        <div className="text-xs text-muted-foreground mt-1">
                          Completed on {format(new Date(milestone.completedAt), 'PPP')}
                        </div>
                      )}
                    </div>
                  </div>
                  {!readOnly && (
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => handleDeleteMilestone(milestone.id)}
                      className="h-8 w-8 text-muted-foreground hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              );
            })
          )}
        </div>
      </CardContent>
    </Card>
  );
}




