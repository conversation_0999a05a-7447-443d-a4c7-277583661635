import { User, Project, ProjectStatus } from '@/lib/types';
import { create } from 'zustand';

interface DashboardStore {
  // State
  selectedProject: string | null;
  filterStatus: ProjectStatus | 'all';
  searchQuery: string;
  
  // Actions
  setSelectedProject: (projectId: string | null) => void;
  setFilterStatus: (status: ProjectStatus | 'all') => void;
  setSearchQuery: (query: string) => void;
}

export const useDashboardStore = create<DashboardStore>((set) => ({
  selectedProject: null,
  filterStatus: 'all',
  searchQuery: '',
  
  setSelectedProject: (projectId) => set({ selectedProject: projectId }),
  setFilterStatus: (status) => set({ filterStatus: status }),
  setSearchQuery: (query) => set({ searchQuery: query }),
}));