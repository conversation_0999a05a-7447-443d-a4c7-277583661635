pipeline {
    agent any
    
    environment {
        // Docker and application settings
        DOCKER_REGISTRY = credentials('docker-registry-url') // Configure in <PERSON> credentials
        DOCKER_CREDENTIALS = credentials('docker-hub-credentials') // Configure in <PERSON> credentials
        GITHUB_CREDENTIALS = credentials('github-credentials') // Configure in Jenkins credentials
        
        // Application environment variables
        NODE_ENV = "${params.ENVIRONMENT == 'production' ? 'production' : 'development'}"
        COMPOSE_PROJECT_NAME = "tms-${env.BUILD_NUMBER}"
        
        // Deployment settings
        DEPLOY_HOST = credentials('deploy-host') // Configure target deployment server
        DEPLOY_USER = credentials('deploy-user') // Configure deployment user
        DEPLOY_KEY = credentials('deploy-ssh-key') // Configure SSH key for deployment
        
        // Notification settings
        SLACK_CHANNEL = '#deployments' // Configure your Slack channel
        EMAIL_RECIPIENTS = '<EMAIL>' // Configure email recipients
    }
    
    parameters {
        choice(
            name: 'ENVIRONMENT',
            choices: ['development', 'production'],
            description: 'Target deployment environment'
        )
        choice(
            name: 'DEPLOY_ACTION',
            choices: ['deploy', 'rollback', 'stop'],
            description: 'Deployment action to perform'
        )
        string(
            name: 'GIT_BRANCH',
            defaultValue: 'main',
            description: 'Git branch to deploy'
        )
        booleanParam(
            name: 'RUN_TESTS',
            defaultValue: true,
            description: 'Run tests before deployment'
        )
        booleanParam(
            name: 'FORCE_REBUILD',
            defaultValue: false,
            description: 'Force rebuild of Docker images'
        )
    }
    
    stages {
        stage('Preparation') {
            steps {
                script {
                    // Clean workspace
                    cleanWs()
                    
                    // Set build display name
                    currentBuild.displayName = "#${env.BUILD_NUMBER} - ${params.ENVIRONMENT}"
                    currentBuild.description = "Branch: ${params.GIT_BRANCH}, Action: ${params.DEPLOY_ACTION}"
                    
                    // Print build information
                    echo "=== Thesis Management System Deployment ==="
                    echo "Environment: ${params.ENVIRONMENT}"
                    echo "Branch: ${params.GIT_BRANCH}"
                    echo "Action: ${params.DEPLOY_ACTION}"
                    echo "Build Number: ${env.BUILD_NUMBER}"
                    echo "Node Environment: ${env.NODE_ENV}"
                }
            }
        }
        
        stage('Checkout Source Code') {
            steps {
                script {
                    try {
                        // Checkout code from GitHub
                        checkout([
                            $class: 'GitSCM',
                            branches: [[name: "*/${params.GIT_BRANCH}"]],
                            doGenerateSubmoduleConfigurations: false,
                            extensions: [
                                [$class: 'CleanBeforeCheckout'],
                                [$class: 'CloneOption', depth: 1, noTags: false, reference: '', shallow: true]
                            ],
                            submoduleCfg: [],
                            userRemoteConfigs: [[
                                credentialsId: env.GITHUB_CREDENTIALS,
                                url: 'https://github.com/your-username/thesis-management-system.git' // Update with your repo URL
                            ]]
                        ])
                        
                        // Get commit information
                        env.GIT_COMMIT_HASH = sh(returnStdout: true, script: 'git rev-parse HEAD').trim()
                        env.GIT_COMMIT_SHORT = sh(returnStdout: true, script: 'git rev-parse --short HEAD').trim()
                        env.GIT_COMMIT_MESSAGE = sh(returnStdout: true, script: 'git log -1 --pretty=%B').trim()
                        env.GIT_AUTHOR = sh(returnStdout: true, script: 'git log -1 --pretty=%an').trim()
                        
                        echo "Successfully checked out commit: ${env.GIT_COMMIT_SHORT}"
                        echo "Commit message: ${env.GIT_COMMIT_MESSAGE}"
                        echo "Author: ${env.GIT_AUTHOR}"
                        
                    } catch (Exception e) {
                        error "Failed to checkout source code: ${e.getMessage()}"
                    }
                }
            }
        }
        
        stage('Environment Setup') {
            steps {
                script {
                    try {
                        echo "Setting up environment for ${params.ENVIRONMENT} deployment..."
                        
                        // Check if required files exist
                        if (!fileExists('package.json')) {
                            error "package.json not found in repository"
                        }
                        if (!fileExists('Dockerfile')) {
                            error "Dockerfile not found in repository"
                        }
                        if (!fileExists('docker-compose.yml')) {
                            error "docker-compose.yml not found in repository"
                        }
                        
                        // Create environment file from template or credentials
                        sh '''
                            if [ ! -f .env ]; then
                                echo "Creating .env file for ${ENVIRONMENT} environment..."
                                
                                # Create .env file with environment-specific variables
                                cat > .env << EOF
# Environment Configuration
NODE_ENV=${NODE_ENV}
ENVIRONMENT=${ENVIRONMENT}

# Appwrite Configuration
APPWRITE_PROJECT_ID=${APPWRITE_PROJECT_ID:-your-project-id}
APPWRITE_DATABASE_ID=${APPWRITE_DATABASE_ID:-your-database-id}

# Security Keys (will be generated if not provided)
_APP_OPENSSL_KEY_V1=${APPWRITE_OPENSSL_KEY:-$(openssl rand -base64 32)}
_APP_EXECUTOR_SECRET=${APPWRITE_EXECUTOR_SECRET:-$(openssl rand -base64 32)}

# Database Configuration
_APP_DB_USER=${DB_USER:-appwrite}
_APP_DB_PASS=${DB_PASS:-$(openssl rand -base64 16)}
_APP_DB_ROOT_PASS=${DB_ROOT_PASS:-$(openssl rand -base64 16)}

# Domain Configuration
_APP_DOMAIN=${APP_DOMAIN:-http://localhost}
_APP_DOMAIN_TARGET=${APP_DOMAIN_TARGET:-http://localhost}
DOMAIN=${DOMAIN:-localhost}

# Email Configuration
_APP_SYSTEM_EMAIL_NAME=${SYSTEM_EMAIL_NAME:-TMS System}
_APP_SYSTEM_EMAIL_ADDRESS=${SYSTEM_EMAIL_ADDRESS:-noreply@localhost}
_APP_SYSTEM_SECURITY_EMAIL_ADDRESS=${SECURITY_EMAIL_ADDRESS:-security@localhost}

# SMTP Configuration (optional)
_APP_SMTP_HOST=${SMTP_HOST:-}
_APP_SMTP_PORT=${SMTP_PORT:-587}
_APP_SMTP_SECURE=${SMTP_SECURE:-tls}
_APP_SMTP_USERNAME=${SMTP_USERNAME:-}
_APP_SMTP_PASSWORD=${SMTP_PASSWORD:-}

# SSL Configuration (for production)
SSL_EMAIL=${SSL_EMAIL:-admin@localhost}
EOF
                                echo ".env file created successfully"
                            else
                                echo ".env file already exists"
                            fi
                        '''
                        
                        // Verify Docker and Docker Compose are available
                        sh '''
                            echo "Checking Docker installation..."
                            docker --version
                            docker-compose --version
                            
                            echo "Checking Docker daemon..."
                            docker info
                        '''
                        
                    } catch (Exception e) {
                        error "Environment setup failed: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Build Application') {
            when {
                anyOf {
                    equals expected: 'deploy', actual: params.DEPLOY_ACTION
                    equals expected: true, actual: params.FORCE_REBUILD
                }
            }
            steps {
                script {
                    try {
                        echo "Building application for ${params.ENVIRONMENT} environment..."

                        // Build Docker images
                        if (params.ENVIRONMENT == 'production') {
                            sh '''
                                echo "Building production images..."
                                docker-compose -f docker-compose.yml -f docker-compose.prod.yml build --no-cache app
                            '''
                        } else {
                            sh '''
                                echo "Building development images..."
                                docker-compose -f docker-compose.yml -f docker-compose.dev.yml build --no-cache app
                            '''
                        }

                        // Tag images with build number and commit hash
                        sh '''
                            docker tag tms_app:latest tms_app:${BUILD_NUMBER}
                            docker tag tms_app:latest tms_app:${GIT_COMMIT_SHORT}
                        '''

                        echo "Application build completed successfully"

                    } catch (Exception e) {
                        error "Application build failed: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Run Tests') {
            when {
                allOf {
                    equals expected: true, actual: params.RUN_TESTS
                    equals expected: 'deploy', actual: params.DEPLOY_ACTION
                }
            }
            steps {
                script {
                    try {
                        echo "Running tests..."

                        // Run tests in a temporary container
                        sh '''
                            # Create a test container
                            docker run --rm \
                                -v $(pwd):/app \
                                -w /app \
                                node:18-alpine \
                                sh -c "
                                    corepack enable pnpm && \
                                    pnpm install --frozen-lockfile && \
                                    pnpm run lint && \
                                    echo 'Tests completed successfully'
                                "
                        '''

                        // If you have actual test scripts, uncomment and modify:
                        // sh 'pnpm run test'
                        // sh 'pnpm run test:e2e'

                        echo "All tests passed successfully"

                    } catch (Exception e) {
                        error "Tests failed: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Security Scan') {
            when {
                equals expected: 'deploy', actual: params.DEPLOY_ACTION
            }
            steps {
                script {
                    try {
                        echo "Running security scans..."

                        // Scan for vulnerabilities in dependencies
                        sh '''
                            echo "Scanning for dependency vulnerabilities..."
                            docker run --rm \
                                -v $(pwd):/app \
                                -w /app \
                                node:18-alpine \
                                sh -c "
                                    corepack enable pnpm && \
                                    pnpm install --frozen-lockfile && \
                                    pnpm audit --audit-level moderate || echo 'Audit completed with warnings'
                                "
                        '''

                        // Scan Docker image for vulnerabilities (if Trivy is available)
                        sh '''
                            if command -v trivy &> /dev/null; then
                                echo "Scanning Docker image for vulnerabilities..."
                                trivy image --exit-code 0 --severity HIGH,CRITICAL tms_app:latest
                            else
                                echo "Trivy not available, skipping image vulnerability scan"
                            fi
                        '''

                        echo "Security scans completed"

                    } catch (Exception e) {
                        echo "Security scan warnings: ${e.getMessage()}"
                        // Don't fail the build for security warnings in development
                        if (params.ENVIRONMENT == 'production') {
                            error "Security scan failed for production deployment"
                        }
                    }
                }
            }
        }

        stage('Deploy Application') {
            when {
                equals expected: 'deploy', actual: params.DEPLOY_ACTION
            }
            steps {
                script {
                    try {
                        echo "Deploying application to ${params.ENVIRONMENT} environment..."

                        // Stop existing services
                        sh '''
                            echo "Stopping existing services..."
                            docker-compose down --remove-orphans || true
                        '''

                        // Deploy based on environment
                        if (params.ENVIRONMENT == 'production') {
                            sh '''
                                echo "Starting production deployment..."
                                docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

                                echo "Waiting for services to be ready..."
                                sleep 30

                                # Health check
                                echo "Performing health checks..."
                                timeout 300 bash -c 'until curl -f http://localhost/health || curl -f http://localhost:3000/health; do sleep 5; done' || {
                                    echo "Health check failed, rolling back..."
                                    docker-compose -f docker-compose.yml -f docker-compose.prod.yml down
                                    exit 1
                                }
                            '''
                        } else {
                            sh '''
                                echo "Starting development deployment..."
                                docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

                                echo "Waiting for services to be ready..."
                                sleep 30

                                # Health check
                                echo "Performing health checks..."
                                timeout 300 bash -c 'until curl -f http://localhost:3000/health; do sleep 5; done' || {
                                    echo "Health check failed, rolling back..."
                                    docker-compose -f docker-compose.yml -f docker-compose.dev.yml down
                                    exit 1
                                }
                            '''
                        }

                        // Display service status
                        sh '''
                            echo "Deployment completed. Service status:"
                            docker-compose ps
                        '''

                        echo "Application deployed successfully to ${params.ENVIRONMENT}"

                    } catch (Exception e) {
                        error "Deployment failed: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Rollback') {
            when {
                equals expected: 'rollback', actual: params.DEPLOY_ACTION
            }
            steps {
                script {
                    try {
                        echo "Rolling back to previous deployment..."

                        // Stop current services
                        sh '''
                            echo "Stopping current services..."
                            docker-compose down --remove-orphans
                        '''

                        // Restore previous version (you might want to implement a more sophisticated rollback strategy)
                        sh '''
                            echo "Rolling back to previous version..."

                            # Try to find and restore previous image
                            PREVIOUS_BUILD=$((BUILD_NUMBER - 1))
                            if docker image inspect tms_app:${PREVIOUS_BUILD} >/dev/null 2>&1; then
                                echo "Found previous image: tms_app:${PREVIOUS_BUILD}"
                                docker tag tms_app:${PREVIOUS_BUILD} tms_app:latest
                            else
                                echo "Previous image not found, using latest available"
                            fi
                        '''

                        // Redeploy with previous version
                        if (params.ENVIRONMENT == 'production') {
                            sh '''
                                echo "Redeploying production with previous version..."
                                docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
                            '''
                        } else {
                            sh '''
                                echo "Redeploying development with previous version..."
                                docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
                            '''
                        }

                        echo "Rollback completed successfully"

                    } catch (Exception e) {
                        error "Rollback failed: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Stop Services') {
            when {
                equals expected: 'stop', actual: params.DEPLOY_ACTION
            }
            steps {
                script {
                    try {
                        echo "Stopping all services..."

                        sh '''
                            echo "Stopping and removing containers..."
                            docker-compose down --remove-orphans

                            echo "Removing unused images..."
                            docker image prune -f

                            echo "Services stopped successfully"
                        '''

                    } catch (Exception e) {
                        error "Failed to stop services: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Post-Deployment Verification') {
            when {
                equals expected: 'deploy', actual: params.DEPLOY_ACTION
            }
            steps {
                script {
                    try {
                        echo "Running post-deployment verification..."

                        // Wait for services to stabilize
                        sleep(time: 60, unit: 'SECONDS')

                        // Verify services are running
                        sh '''
                            echo "Verifying service health..."

                            # Check if containers are running
                            if ! docker-compose ps | grep -q "Up"; then
                                echo "ERROR: Some services are not running"
                                docker-compose ps
                                exit 1
                            fi

                            # Check application endpoints
                            if [ "${ENVIRONMENT}" = "production" ]; then
                                curl -f http://localhost/ || curl -f http://localhost:80/ || {
                                    echo "ERROR: Production application is not responding"
                                    exit 1
                                }
                            else
                                curl -f http://localhost:3000/ || {
                                    echo "ERROR: Development application is not responding"
                                    exit 1
                                }
                            fi

                            echo "All health checks passed"
                        '''

                        // Log deployment information
                        sh '''
                            echo "=== Deployment Summary ==="
                            echo "Environment: ${ENVIRONMENT}"
                            echo "Build Number: ${BUILD_NUMBER}"
                            echo "Git Commit: ${GIT_COMMIT_SHORT}"
                            echo "Deployed At: $(date)"
                            echo "=========================="

                            # Save deployment info
                            cat > deployment-info.json << EOF
{
    "environment": "${ENVIRONMENT}",
    "buildNumber": "${BUILD_NUMBER}",
    "gitCommit": "${GIT_COMMIT_HASH}",
    "gitCommitShort": "${GIT_COMMIT_SHORT}",
    "gitBranch": "${GIT_BRANCH}",
    "deployedAt": "$(date -Iseconds)",
    "deployedBy": "${BUILD_USER:-jenkins}",
    "status": "success"
}
EOF
                        '''

                        echo "Post-deployment verification completed successfully"

                    } catch (Exception e) {
                        error "Post-deployment verification failed: ${e.getMessage()}"
                    }
                }
            }
        }
    }

    post {
        always {
            script {
                // Archive deployment artifacts
                archiveArtifacts artifacts: 'deployment-info.json', allowEmptyArchive: true

                // Clean up Docker resources
                sh '''
                    echo "Cleaning up Docker resources..."

                    # Remove old images (keep last 5 builds)
                    docker images tms_app --format "table {{.Tag}}" | grep -E "^[0-9]+$" | sort -nr | tail -n +6 | xargs -r docker rmi tms_app: || true

                    # Clean up dangling images
                    docker image prune -f || true

                    echo "Docker cleanup completed"
                '''

                // Generate build report
                def buildStatus = currentBuild.currentResult ?: 'SUCCESS'
                def buildDuration = currentBuild.durationString.replace(' and counting', '')

                writeFile file: 'build-report.txt', text: """
=== Jenkins Build Report ===
Project: Thesis Management System
Build Number: ${env.BUILD_NUMBER}
Environment: ${params.ENVIRONMENT}
Action: ${params.DEPLOY_ACTION}
Branch: ${params.GIT_BRANCH}
Commit: ${env.GIT_COMMIT_SHORT}
Status: ${buildStatus}
Duration: ${buildDuration}
Started: ${new Date(currentBuild.startTimeInMillis)}
Completed: ${new Date()}
============================
"""

                archiveArtifacts artifacts: 'build-report.txt', allowEmptyArchive: true
            }
        }

        success {
            script {
                echo "🎉 Deployment completed successfully!"

                // Send success notifications
                def message = """
✅ *Thesis Management System Deployment Successful*

*Environment:* ${params.ENVIRONMENT}
*Action:* ${params.DEPLOY_ACTION}
*Branch:* ${params.GIT_BRANCH}
*Commit:* ${env.GIT_COMMIT_SHORT}
*Build:* #${env.BUILD_NUMBER}
*Duration:* ${currentBuild.durationString.replace(' and counting', '')}

*Access URLs:*
${params.ENVIRONMENT == 'production' ?
    '• Application: http://your-domain.com\n• Admin Console: http://your-domain.com/console' :
    '• Application: http://localhost:3000\n• Admin Console: http://localhost:8080/console'}

*Deployed by:* ${env.BUILD_USER ?: 'Jenkins'}
*Time:* ${new Date()}
"""

                // Slack notification (if configured)
                try {
                    slackSend(
                        channel: env.SLACK_CHANNEL,
                        color: 'good',
                        message: message,
                        teamDomain: 'your-team', // Configure your Slack team
                        token: 'slack-token' // Configure in Jenkins credentials
                    )
                } catch (Exception e) {
                    echo "Slack notification failed: ${e.getMessage()}"
                }

                // Email notification
                try {
                    emailext(
                        subject: "✅ TMS Deployment Success - ${params.ENVIRONMENT} #${env.BUILD_NUMBER}",
                        body: message.replace('*', '').replace('✅', ''),
                        to: env.EMAIL_RECIPIENTS,
                        mimeType: 'text/plain'
                    )
                } catch (Exception e) {
                    echo "Email notification failed: ${e.getMessage()}"
                }
            }
        }

        failure {
            script {
                echo "❌ Deployment failed!"

                // Collect failure information
                def failureMessage = """
❌ *Thesis Management System Deployment Failed*

*Environment:* ${params.ENVIRONMENT}
*Action:* ${params.DEPLOY_ACTION}
*Branch:* ${params.GIT_BRANCH}
*Commit:* ${env.GIT_COMMIT_SHORT}
*Build:* #${env.BUILD_NUMBER}
*Duration:* ${currentBuild.durationString.replace(' and counting', '')}

*Error:* ${currentBuild.description ?: 'Check build logs for details'}

*Build URL:* ${env.BUILD_URL}

*Triggered by:* ${env.BUILD_USER ?: 'Jenkins'}
*Time:* ${new Date()}
"""

                // Slack notification (if configured)
                try {
                    slackSend(
                        channel: env.SLACK_CHANNEL,
                        color: 'danger',
                        message: failureMessage,
                        teamDomain: 'your-team', // Configure your Slack team
                        token: 'slack-token' // Configure in Jenkins credentials
                    )
                } catch (Exception e) {
                    echo "Slack notification failed: ${e.getMessage()}"
                }

                // Email notification
                try {
                    emailext(
                        subject: "❌ TMS Deployment Failed - ${params.ENVIRONMENT} #${env.BUILD_NUMBER}",
                        body: failureMessage.replace('*', '').replace('❌', ''),
                        to: env.EMAIL_RECIPIENTS,
                        mimeType: 'text/plain',
                        attachLog: true
                    )
                } catch (Exception e) {
                    echo "Email notification failed: ${e.getMessage()}"
                }

                // Attempt automatic rollback for production failures
                if (params.ENVIRONMENT == 'production' && params.DEPLOY_ACTION == 'deploy') {
                    echo "Attempting automatic rollback for production failure..."
                    try {
                        sh '''
                            echo "Rolling back production deployment..."
                            docker-compose -f docker-compose.yml -f docker-compose.prod.yml down --remove-orphans

                            # Try to restore previous version
                            PREVIOUS_BUILD=$((BUILD_NUMBER - 1))
                            if docker image inspect tms_app:${PREVIOUS_BUILD} >/dev/null 2>&1; then
                                docker tag tms_app:${PREVIOUS_BUILD} tms_app:latest
                                docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
                                echo "Automatic rollback completed"
                            else
                                echo "No previous version available for rollback"
                            fi
                        '''
                    } catch (Exception e) {
                        echo "Automatic rollback failed: ${e.getMessage()}"
                    }
                }
            }
        }

        cleanup {
            script {
                // Final cleanup
                echo "Performing final cleanup..."

                // Remove temporary files
                sh '''
                    rm -f deployment-info.json build-report.txt || true
                '''

                echo "Pipeline cleanup completed"
            }
        }
    }
}
