@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Enhanced light mode colors for modern dashboard UI */
    --background: 0 0% 99%;
    --foreground: 240 15% 9%;
    --card: 0 0% 100%;
    --card-foreground: 240 15% 9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 15% 9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 98%;
    --muted-foreground: 215.4 25% 35%; /* Enhanced contrast for better readability */
    --accent: 210 40% 98%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 0 0% 0% / 0.08; /* Subtle borders with transparency */
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem; /* Slightly larger radius for modern look */

    /* Dashboard-specific color variables */
    --dashboard-card-shadow: 0 0% 0% / 0.05;
    --dashboard-hover-bg: 210 40% 96%;
    --dashboard-accent: 221.2 83.2% 53.3%;
    --dashboard-success: 142 76% 36%;
    --dashboard-warning: 38 92% 50%;
    --dashboard-error: 0 84% 60%;

    /* Enhanced chat-specific colors for light mode */
    --chat-bg: 0 0% 97%;
    --chat-bubble-own: 142 70% 40%; /* Slightly darker WhatsApp green for better contrast */
    --chat-bubble-own-text: 0 0% 100%; /* White text */
    --chat-bubble-other: 0 0% 100%; /* White background */
    --chat-bubble-other-text: 220 25% 20%; /* Dark text */
    --chat-timestamp: 220 15% 55%; /* For incoming messages */
    --chat-timestamp-own: 0 0% 85%; /* Light color for outgoing message timestamps */
    --chat-header-bg: 0 0% 96%;
    --chat-input-bg: 0 0% 100%;
    --chat-divider: 220 15% 90%;
  }

  .dark {
    /* Enhanced dark mode colors for modern dashboard UI */
    --background: 220 20% 8%; /* Deeper background for better contrast */
    --foreground: 0 0% 98%;
    --card: 220 15% 12%; /* Slightly lighter cards for better separation */
    --card-foreground: 0 0% 98%;
    --popover: 220 15% 12%;
    --popover-foreground: 0 0% 98%;
    --primary: 221.2 83.2% 53.3%; /* Consistent primary across themes */
    --primary-foreground: 220 20% 8%;
    --secondary: 220 15% 18%;
    --secondary-foreground: 0 0% 98%;
    --muted: 220 15% 18%;
    --muted-foreground: 220 10% 70%; /* Optimized for readability */
    --accent: 220 15% 20%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 70% 50%; /* Enhanced visibility */
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 100% / 0.1; /* Subtle borders with transparency */
    --input: 220 15% 15%;
    --ring: 221.2 83.2% 53.3%;

    /* Dashboard-specific dark mode variables */
    --dashboard-card-shadow: 0 0% 0% / 0.3;
    --dashboard-hover-bg: 220 15% 15%;
    --dashboard-accent: 221.2 83.2% 53.3%;
    --dashboard-success: 142 76% 45%;
    --dashboard-warning: 38 92% 55%;
    --dashboard-error: 0 84% 65%;
    /* Enhanced chat-specific colors for dark mode */
    --chat-bg: 220 20% 8%;
    --chat-bubble-own: 142 60% 42%; /* Slightly darker green for better contrast */
    --chat-bubble-own-text: 0 0% 100%; /* White text */
    --chat-bubble-other: 220 15% 18%; /* Darker background */
    --chat-bubble-other-text: 0 0% 95%; /* Light text */
    --chat-timestamp: 220 15% 70%; /* For incoming messages */
    --chat-timestamp-own: 0 0% 80%; /* Light color for outgoing message timestamps */
    --chat-header-bg: 220 20% 12%;
    --chat-input-bg: 220 15% 15%;
    --chat-divider: 220 15% 25%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Enhanced chat bubble styles */
  .chat-bubble {
    @apply relative px-3 py-2.5 max-w-[75%] break-words shadow-sm;
    word-wrap: break-word;
    overflow-wrap: break-word;
    line-height: 1.4;
  }

  .chat-bubble-own {
    @apply bg-[hsl(var(--chat-bubble-own))] text-[hsl(var(--chat-bubble-own-text))];
  }

  .chat-bubble-other {
    @apply bg-[hsl(var(--chat-bubble-other))] text-[hsl(var(--chat-bubble-other-text))] border border-[hsl(var(--chat-divider))];
  }

  /* Enhanced bubble hover effects */
  .chat-bubble:hover {
    @apply shadow-md;
    transform: translateY(-1px);
  }

  /* Improved timestamp readability for outgoing messages */
  .chat-bubble-own .message-timestamp {
    color: hsl(var(--chat-timestamp-own));
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .chat-bubble-other .message-timestamp {
    color: hsl(var(--chat-timestamp));
  }

  /* Enhanced message group styling */
  .message-group {
    @apply mb-4 space-y-1;
  }

  /* Chat background */
  .chat-background {
    @apply bg-[hsl(var(--chat-bg))];
  }

  /* Chat header styling */
  .chat-header {
    @apply bg-[hsl(var(--chat-header-bg))] border-b border-[hsl(var(--chat-divider))];
  }

  /* Chat input styling */
  .chat-input-container {
    @apply bg-[hsl(var(--chat-input-bg))] border-t border-[hsl(var(--chat-divider))];
  }

  /* Smooth animations for chat */
  .chat-message-enter {
    @apply animate-in fade-in-50 slide-in-from-bottom-2 duration-300;
  }

  .chat-message-exit {
    @apply animate-out fade-out-50 slide-out-to-bottom-2 duration-200;
  }

  /* Responsive chat improvements */
  @media (max-width: 768px) {
    .chat-bubble {
      @apply max-w-[85%];
    }
  }

  /* Improved scrollbar styling for chat */
  .chat-scroll::-webkit-scrollbar {
    width: 6px;
  }

  .chat-scroll::-webkit-scrollbar-track {
    background: transparent;
  }

  .chat-scroll::-webkit-scrollbar-thumb {
    background: hsl(var(--chat-divider));
    border-radius: 3px;
  }

  .chat-scroll::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--chat-timestamp));
  }

  /* Enhanced Dashboard UI Components */
  .dashboard-card {
    @apply bg-card rounded-xl shadow-sm border-0 transition-all duration-200;
    box-shadow: 0 1px 3px 0 hsl(var(--dashboard-card-shadow));
  }

  .dashboard-card:hover {
    @apply shadow-md;
    box-shadow: 0 4px 12px 0 hsl(var(--dashboard-card-shadow));
    transform: translateY(-1px);
  }

  .dashboard-card-header {
    @apply p-6 pb-4;
  }

  .dashboard-card-content {
    @apply p-6 pt-0;
  }

  .dashboard-stat-card {
    @apply dashboard-card cursor-pointer;
  }

  .dashboard-stat-card:hover {
    @apply bg-[hsl(var(--dashboard-hover-bg))];
  }

  .dashboard-grid {
    @apply grid gap-6;
  }

  .dashboard-grid-2 {
    @apply dashboard-grid md:grid-cols-2;
  }

  .dashboard-grid-3 {
    @apply dashboard-grid md:grid-cols-2 lg:grid-cols-3;
  }

  .dashboard-grid-4 {
    @apply dashboard-grid md:grid-cols-2 lg:grid-cols-4;
  }

  /* Enhanced Status Indicators */
  .status-indicator {
    @apply inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium;
  }

  .status-active {
    @apply bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400;
  }

  .status-pending {
    @apply bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400;
  }

  .status-completed {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400;
  }

  .status-overdue {
    @apply bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 animate-pulse;
  }

  /* Enhanced Interactive Elements */
  .dashboard-button {
    @apply inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200;
  }

  .dashboard-button-primary {
    @apply dashboard-button bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm;
  }

  .dashboard-button-secondary {
    @apply dashboard-button bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .dashboard-button-outline {
    @apply dashboard-button border border-border bg-background hover:bg-accent hover:text-accent-foreground;
  }

  /* Enhanced List Items */
  .dashboard-list-item {
    @apply flex items-center gap-4 p-4 rounded-lg transition-all duration-200 cursor-pointer;
    @apply hover:bg-[hsl(var(--dashboard-hover-bg))] hover:shadow-sm;
  }

  .dashboard-list-item-urgent {
    @apply dashboard-list-item bg-red-50 border border-red-200 hover:bg-red-100;
    @apply dark:bg-red-950/20 dark:border-red-800 dark:hover:bg-red-900/30;
  }

  /* Improved Typography */
  .dashboard-title {
    @apply text-2xl font-bold tracking-tight text-foreground;
  }

  .dashboard-subtitle {
    @apply text-muted-foreground mt-1;
  }

  .dashboard-section-title {
    @apply text-lg font-semibold text-foreground mb-4;
  }

  /* Enhanced Loading States */
  .dashboard-skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  /* Responsive Improvements */
  @media (max-width: 768px) {
    .dashboard-card {
      @apply rounded-lg;
    }

    .dashboard-grid {
      @apply gap-4;
    }
  }
}



