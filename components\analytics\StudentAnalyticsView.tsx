"use client";

import { useMemo } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useAnalytics } from "@/hooks/useAnalytics";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  Legend,
} from "recharts";
import {
  BookOpen,
  Calendar,
  CheckCircle2,
  Clock,
  FileText,
  Target,
  TrendingUp,
  AlertTriangle,
} from "lucide-react";
import { format } from "date-fns";

interface StudentAnalyticsViewProps {
  timeRange?: "month" | "quarter" | "year";
}

const STATUS_COLORS = {
  active: "#10b981",
  completed: "#3b82f6", 
  draft: "#f59e0b",
  under_review: "#8b5cf6",
  approved: "#10b981",
  rejected: "#ef4444",
};

export function StudentAnalyticsView({ timeRange = "month" }: StudentAnalyticsViewProps) {
  const { user } = useAuth();
  
  const {
    analytics,
    isLoading,
    error,
    refreshAnalytics
  } = useAnalytics({
    timeRange,
    enableRealTime: true,
    includeExtendedAnalytics: true,
    filters: { userRole: 'student' }
  });

  // Calculate student-specific metrics
  const studentMetrics = useMemo(() => {
    if (!analytics || !user) return null;

    const totalProjects = analytics.totalProjects;
    const activeProjects = analytics.projectsByStatus.active || 0;
    const completedProjects = analytics.projectsByStatus.completed || 0;
    const totalDocuments = analytics.documentAnalytics.totalDocuments;
    const approvedDocuments = analytics.documentAnalytics.documentsByStatus.approved || 0;
    const pendingDocuments = analytics.documentAnalytics.documentsByStatus.under_review || 0;
    const totalMilestones = analytics.milestoneAnalytics.totalMilestones;
    const completedMilestones = analytics.milestoneAnalytics.milestonesByStatus.completed || 0;
    const overdueMilestones = analytics.milestoneAnalytics.overdueMilestones || 0;

    const projectCompletionRate = totalProjects > 0 ? (completedProjects / totalProjects) * 100 : 0;
    const documentApprovalRate = totalDocuments > 0 ? (approvedDocuments / totalDocuments) * 100 : 0;
    const milestoneCompletionRate = totalMilestones > 0 ? (completedMilestones / totalMilestones) * 100 : 0;

    return {
      totalProjects,
      activeProjects,
      completedProjects,
      totalDocuments,
      approvedDocuments,
      pendingDocuments,
      totalMilestones,
      completedMilestones,
      overdueMilestones,
      projectCompletionRate,
      documentApprovalRate,
      milestoneCompletionRate,
    };
  }, [analytics, user]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-8 bg-muted rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error || !analytics || !studentMetrics) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>Unable to load your analytics data</p>
            <button 
              onClick={refreshAnalytics}
              className="mt-2 text-primary hover:underline"
            >
              Try again
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const documentStatusData = Object.entries(analytics.documentAnalytics.documentsByStatus).map(([status, count]) => ({
    name: status.replace('_', ' ').toUpperCase(),
    value: count,
    color: STATUS_COLORS[status as keyof typeof STATUS_COLORS] || "#6b7280"
  }));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">My Progress Analytics</h2>
          <p className="text-muted-foreground">
            Your academic progress and performance insights
          </p>
        </div>
        <Badge variant="outline" className="text-sm">
          {timeRange.charAt(0).toUpperCase() + timeRange.slice(1)} View
        </Badge>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{studentMetrics.activeProjects}</div>
            <p className="text-xs text-muted-foreground">
              {studentMetrics.totalProjects} total projects
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Documents Submitted</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{studentMetrics.totalDocuments}</div>
            <p className="text-xs text-muted-foreground">
              {studentMetrics.approvedDocuments} approved
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Milestones</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{studentMetrics.completedMilestones}</div>
            <p className="text-xs text-muted-foreground">
              of {studentMetrics.totalMilestones} completed
            </p>
          </CardContent>
        </Card>

        <Card className={studentMetrics.overdueMilestones > 0 ? "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20" : ""}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className={`text-sm font-medium ${studentMetrics.overdueMilestones > 0 ? "text-red-700 dark:text-red-300" : ""}`}>
              {studentMetrics.overdueMilestones > 0 ? "Overdue Items" : "On Track"}
            </CardTitle>
            {studentMetrics.overdueMilestones > 0 ? (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            ) : (
              <CheckCircle2 className="h-4 w-4 text-green-500" />
            )}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${studentMetrics.overdueMilestones > 0 ? "text-red-600 dark:text-red-400" : "text-green-600 dark:text-green-400"}`}>
              {studentMetrics.overdueMilestones > 0 ? studentMetrics.overdueMilestones : "✓"}
            </div>
            <p className={`text-xs ${studentMetrics.overdueMilestones > 0 ? "text-red-600 dark:text-red-400" : "text-muted-foreground"}`}>
              {studentMetrics.overdueMilestones > 0 ? "milestones overdue" : "All up to date"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Progress Overview */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Progress Overview</CardTitle>
            <CardDescription>Your completion rates across different areas</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Project Completion</span>
                <span>{studentMetrics.projectCompletionRate.toFixed(1)}%</span>
              </div>
              <Progress value={studentMetrics.projectCompletionRate} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Document Approval Rate</span>
                <span>{studentMetrics.documentApprovalRate.toFixed(1)}%</span>
              </div>
              <Progress value={studentMetrics.documentApprovalRate} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Milestone Completion</span>
                <span>{studentMetrics.milestoneCompletionRate.toFixed(1)}%</span>
              </div>
              <Progress value={studentMetrics.milestoneCompletionRate} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Document Status Distribution</CardTitle>
            <CardDescription>Status breakdown of your submitted documents</CardDescription>
          </CardHeader>
          <CardContent>
            {documentStatusData.length > 0 ? (
              <div className="h-[200px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={documentStatusData}
                      cx="50%"
                      cy="50%"
                      outerRadius={60}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {documentStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-8 w-8 mx-auto mb-2" />
                <p>No documents submitted yet</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
