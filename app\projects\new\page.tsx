"use client";

import { useState, useCallback, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Header } from "@/components/layout/Header";
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { DatePicker } from "@/components/ui/date-picker";
import { useToast } from "@/hooks/useToast";
import { useProject } from "@/hooks/useProject";
import { ProjectStatus } from "@/lib/types";
import { useMilestones } from "@/hooks/useMilestones";
import { Plus, X } from "lucide-react";

export default function NewProjectPage() {
  const { user } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const {
    createProject,
    updateProject,
    deleteProject,
    useProjectsQuery,
  } = useProject();
  const { createMilestones } = useMilestones();

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    deadline: undefined as Date | undefined,
  });

  const [milestones, setMilestones] = useState<{ title: string; date: Date }[]>(
    []
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Memoize form validation
  const isFormValid = useMemo(() => {
    return (
      formData.title.trim().length > 0 && formData.description.trim().length > 0
    );
  }, [formData.title, formData.description]);

  // Handle form field changes
  const handleChange = useCallback(
    (field: keyof typeof formData, value: any) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
    },
    []
  );

  // Add milestone
  const handleAddMilestone = useCallback(() => {
    setMilestones((prev) => [...prev, { title: "", date: new Date() }]);
  }, []);

  // Update milestone
  const handleMilestoneChange = useCallback(
    (index: number, field: string, value: any) => {
      setMilestones((prev) =>
        prev.map((m, i) => (i === index ? { ...m, [field]: value } : m))
      );
    },
    []
  );

  // Remove milestone
  const handleRemoveMilestone = useCallback((index: number) => {
    setMilestones((prev) => prev.filter((_, i) => i !== index));
  }, []);

  // Submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log("Form data:", formData);
    console.log("Milestones:", milestones);
    console.log("User:", user);
    console.log("Is form valid:", isFormValid);

    // Validate user and form
    if (!user || !isFormValid || isSubmitting) return;

    setIsSubmitting(true);

    try {
      const project = await createProject.mutateAsync({
        title: formData.title.trim(),
        description: formData.description.trim(),
        studentId: user.id,
        supervisorIds: [],
        status: "pending_supervisor" as ProjectStatus,
        deadline: formData.deadline || new Date(),
        teamMembers: [user.id],
        lastActivity: new Date(),
      });

      // Create milestones if any
      if (milestones.length > 0) {
        const validMilestones = milestones.filter((m) => m.title.trim());
        if (validMilestones.length > 0) {
          await createMilestones.mutateAsync(
            validMilestones.map((m) => ({
              projectId: project.id,
              title: m.title.trim(),
              dueDate: m.date,
              completed: false,
            }))
          );
        }
      }

      toast({
        title: "Project created",
        description:
          "Your project has been created successfully and is pending supervisor assignment.",
      });

      router.push(`/projects/${project.id}`);
    } catch (error) {
      console.error("Project creation error:", error);
      toast({
        title: "Error creating project",
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="container py-6">
        <Card>
          <CardHeader>
            <CardTitle>Create New Project</CardTitle>
            <CardDescription>
              Start a new thesis project (a manager will assign supervisors)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-2">
                <Label htmlFor="title">Project Title</Label>
                <Input
                  id="title"
                  placeholder="Enter project title"
                  value={formData.title}
                  onChange={(e) => handleChange("title", e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Project Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe your project"
                  rows={4}
                  value={formData.description}
                  onChange={(e) => handleChange("description", e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label>Project Deadline</Label>
                <DatePicker
                  value={formData.deadline}
                  onChange={(date) => handleChange("deadline", date)}
                />
              </div>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <Label>Milestones</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleAddMilestone}
                  >
                    <Plus className="h-4 w-4 mr-1" /> Add Milestone
                  </Button>
                </div>

                {milestones.map((milestone, index) => (
                  <div key={index} className="flex gap-2 items-start">
                    <div className="flex-1">
                      <Input
                        placeholder="Milestone title"
                        value={milestone.title}
                        onChange={(e) =>
                          handleMilestoneChange(index, "title", e.target.value)
                        }
                      />
                    </div>
                    <div className="w-40">
                      <DatePicker
                        value={milestone.date}
                        onChange={(date) =>
                          handleMilestoneChange(index, "date", date)
                        }
                      />
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveMilestone(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={!isFormValid || isSubmitting}
              >
                {isSubmitting ? "Creating..." : "Create Project"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
