// import { useState, useMemo, useCallback, useEffect } from "react";
// import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
// import { Button } from "@/components/ui/button";
// import { Textarea } from "@/components/ui/textarea";
// import { format, formatDistanceToNow } from "date-fns";
// import { useAuth } from "@/hooks/useAuth";
// import { Comment, User } from "@/lib/types";
// import {
//   CheckCircle2,
//   ReplyIcon,
//   TrashIcon,
//   Loader2,
//   AlertCircle,
//   PencilIcon,
// } from "lucide-react";
// import { useComment } from "@/hooks/useComment";
// import { useUser } from "@/hooks/useUser";
// import { Badge } from "@/components/ui/badge";
// import { Skeleton } from "@/components/ui/skeleton";
// import { Alert, AlertDescription } from "@/components/ui/alert";
// import {
//   AlertDialog,
//   AlertDialogAction,
//   AlertDialogCancel,
//   AlertDialogContent,
//   AlertDialogDescription,
//   AlertDialogFooter,
//   AlertDialogHeader,
//   AlertDialogTitle,
// } from "@/components/ui/alert-dialog";
// import { useToast } from "@/hooks/useToast";

// // Extended Comment type to include additional properties
// interface ExtendedComment extends Comment {
//   parentId?: string;
//   resolved?: boolean;
//   replies?: ExtendedComment[];
//   createdAt?: string | Date; // From Appwrite $createdAt
//   // $createdAt?: string; // Appwrite metadata field
// }

// interface CommentThreadProps {
//   documentId: string;
//   highlightId?: string;
// }

// export function CommentThread({ documentId, highlightId }: CommentThreadProps) {
//   const { user } = useAuth();
//   const { toast } = useToast();
//   const [replyTo, setReplyTo] = useState<string | null>(null);
//   const [newComment, setNewComment] = useState("");
//   const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
//   const [commentToDelete, setCommentToDelete] = useState<string | null>(null);
//   const [editingComment, setEditingComment] = useState<string | null>(null);
//   const [editContent, setEditContent] = useState("");

//   // Use the comment hooks
//   const {
//     create: createCommentMutation,
//     update: updateCommentMutation,
//     remove: deleteCommentMutation,
//     getDocumentComments,
//   } = useComment();

//   // Get comments for this document
//   const {
//     data: comments = [],
//     isLoading: isLoadingComments,
//     error: commentsError,
//   } = getDocumentComments(documentId);

//   // Extract unique user IDs from comments
//   const userIds = useMemo(
//     () => Array.from(new Set(comments.map((c: Comment) => c.userId))),
//     [comments]
//   );

//   // Fetch users involved in comments using the batch query
//   const { useUsersByIdsQuery } = useUser();
//   const {
//     data: users = [],
//     isLoading: isLoadingUsers,
//     error: usersError,
//   } = useUsersByIdsQuery(userIds);

//   // Convert users array to a lookup object
//   const usersMap = useMemo(() => {
//     const map: Record<string, User> = {};
//     users.forEach((user) => {
//       if (user) {
//         map[user.id] = user;
//       }
//     });
//     return map;
//   }, [users]);

//   // Organize comments into a tree structure
//   const commentTree = useMemo(() => {
//     const rootComments: ExtendedComment[] = [];
//     const commentMap: Record<string, ExtendedComment> = {};

//     // First pass: create a map of all comments with empty replies array
//     comments.forEach((comment: Comment) => {
//       // Use $createdAt from Appwrite or fallback to current time
//       const createdAt = (comment as any).$createdAt || new Date().toISOString();

//       commentMap[comment.id] = {
//         ...comment,
//         replies: [],
//         createdAt: createdAt,
//       };
//     });

//     // Second pass: organize into tree structure
//     comments.forEach((comment: Comment) => {
//       const parentId = comment.parentId;
//       if (parentId && commentMap[parentId]) {
//         commentMap[parentId].replies!.push(commentMap[comment.id]);
//       } else {
//         rootComments.push(commentMap[comment.id]);
//       }
//     });

//     // Sort root comments by creation date (newest first)
//     return rootComments.sort((a, b) => {
//       const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
//       const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
//       return dateB - dateA;
//     });
//   }, [comments]);

//   // Handle submitting a new comment or reply
//   const handleSubmitComment = useCallback(() => {
//     if (!user || !newComment.trim()) return;

//     // Create the comment data
//     const commentData = {
//       documentId,
//       userId: user.id,
//       content: newComment.trim(),
//       highlight: highlightId
//         ? {
//             start: 0,
//             end: 0,
//             text: "",
//           }
//         : undefined,
//       parentId: replyTo || undefined,
//       resolved: false,
//     };

//     toast({
//       title: replyTo ? "Replying to comment..." : "Adding comment...",
//       description: "Your comment is being saved.",
//     });

//     createCommentMutation.mutate(commentData, {
//       onSuccess: () => {
//         toast({
//           title: "Success",
//           description: replyTo
//             ? "Reply added successfully"
//             : "Comment added successfully",
//         });
//         setNewComment("");
//         setReplyTo(null);
//       },
//       onError: (error) => {
//         toast({
//           title: "Error",
//           description: `Failed to save comment: ${error.message}`,
//           variant: "destructive",
//         });
//       },
//     });
//   }, [
//     user,
//     newComment,
//     documentId,
//     highlightId,
//     replyTo,
//     createCommentMutation,
//     toast,
//   ]);

//   // Handle deleting a comment
//   const handleDeleteComment = useCallback(
//     (commentId: string) => {
//       setDeleteDialogOpen(false);

//       if (!user) return;

//       toast({
//         title: "Deleting comment...",
//         description: "Your comment is being deleted.",
//       });

//       deleteCommentMutation.mutate(commentId, {
//         onSuccess: () => {
//           toast({
//             title: "Success",
//             description: "Comment deleted successfully",
//           });
//         },
//         onError: (error) => {
//           toast({
//             title: "Error",
//             description: `Failed to delete comment: ${error.message}`,
//             variant: "destructive",
//           });
//         },
//       });
//     },
//     [user, deleteCommentMutation, toast]
//   );

//   // Handle toggling the resolved status of a comment
//   const handleToggleResolved = useCallback(
//     (comment: ExtendedComment) => {
//       if (!user) return;

//       toast({
//         title: comment.resolved
//           ? "Marking as unresolved..."
//           : "Marking as resolved...",
//         description: "Updating comment status...",
//       });

//       updateCommentMutation.mutate(
//         {
//           id: comment.id,
//           updates: {
//             resolved: !comment.resolved,
//           },
//         },
//         {
//           onSuccess: () => {
//             toast({
//               title: "Success",
//               description: comment.resolved
//                 ? "Comment marked as unresolved"
//                 : "Comment marked as resolved",
//             });
//           },
//           onError: (error) => {
//             toast({
//               title: "Error",
//               description: `Failed to update comment: ${error.message}`,
//               variant: "destructive",
//             });
//           },
//         }
//       );
//     },
//     [user, updateCommentMutation, toast]
//   );

//   // Handle editing a comment
//   const handleStartEditing = useCallback((comment: ExtendedComment) => {
//     setEditingComment(comment.id);
//     setEditContent(comment.content);
//   }, []);

//   const handleSaveEdit = useCallback(
//     (comment: ExtendedComment) => {
//       if (!user || !editContent.trim()) return;

//       toast({
//         title: "Updating comment...",
//         description: "Your changes are being saved.",
//       });

//       updateCommentMutation.mutate(
//         {
//           id: comment.id,
//           updates: {
//             content: editContent.trim(),
//           },
//         },
//         {
//           onSuccess: () => {
//             toast({
//               title: "Success",
//               description: "Comment updated successfully",
//             });
//             setEditingComment(null);
//             setEditContent("");
//           },
//           onError: (error) => {
//             toast({
//               title: "Error",
//               description: `Failed to update comment: ${error.message}`,
//               variant: "destructive",
//             });
//           },
//         }
//       );
//     },
//     [user, editContent, updateCommentMutation, toast]
//   );

//   // Render a single comment with its replies
//   const renderComment = useCallback(
//     (comment: ExtendedComment, depth = 0) => {
//       const commentUser = usersMap[comment.userId];
//       const isResolved = comment.resolved;
//       const createdAtDate = comment.createdAt
//         ? new Date(comment.createdAt)
//         : new Date();
//       const timeAgo = formatDistanceToNow(createdAtDate, { addSuffix: true });

//       // Prepare to confirm deletion
//       const openDeleteDialog = () => {
//         setCommentToDelete(comment.id);
//         setDeleteDialogOpen(true);
//       };

//       return (
//         <div
//           key={comment.id}
//           className={`space-y-2 ${
//             depth > 0 ? "ml-8 pl-4 border-l-2 border-gray-200" : ""
//           }`}
//         >
//           <div className="flex items-start gap-2">
//             <Avatar className="h-8 w-8">
//               {commentUser && (
//                 <>
//                   <AvatarImage
//                     src={commentUser.profileImage}
//                     alt={commentUser.name}
//                   />
//                   <AvatarFallback>{commentUser.name[0]}</AvatarFallback>
//                 </>
//               )}
//               {!commentUser && <AvatarFallback>?</AvatarFallback>}
//             </Avatar>
//             <div className="flex-1">
//               <div className="flex items-center gap-2 flex-wrap">
//                 <span className="font-medium">
//                   {commentUser ? commentUser.name : "Unknown User"}
//                 </span>
//                 <span
//                   className="text-sm text-muted-foreground"
//                   title={format(createdAtDate, "PPpp")}
//                 >
//                   {timeAgo}
//                 </span>
//                 {isResolved && (
//                   <Badge
//                     variant="outline"
//                     className="bg-green-50 text-green-700 border-green-200"
//                   >
//                     <CheckCircle2 className="h-3 w-3 mr-1" />
//                     Resolved
//                   </Badge>
//                 )}
//               </div>
//               {editingComment === comment.id ? (
//                 <div className="mt-1">
//                   <Textarea
//                     value={editContent}
//                     onChange={(e) => setEditContent(e.target.value)}
//                     className="min-h-[80px] mb-2"
//                   />
//                   <div className="flex justify-end gap-2">
//                     <Button
//                       variant="outline"
//                       size="sm"
//                       onClick={() => setEditingComment(null)}
//                     >
//                       Cancel
//                     </Button>
//                     <Button
//                       variant="default"
//                       size="sm"
//                       onClick={() => handleSaveEdit(comment)}
//                       disabled={
//                         !editContent.trim() || updateCommentMutation.isPending
//                       }
//                     >
//                       {updateCommentMutation.isPending &&
//                       updateCommentMutation.variables?.id === comment.id ? (
//                         <Loader2 className="h-4 w-4 mr-1 animate-spin" />
//                       ) : (
//                         "Save"
//                       )}
//                     </Button>
//                   </div>
//                 </div>
//               ) : (
//                 <p className="mt-1">{comment.content}</p>
//               )}

//               <div className="mt-2 flex items-center gap-2 flex-wrap">
//                 {editingComment !== comment.id && (
//                   <Button
//                     variant="ghost"
//                     size="sm"
//                     onClick={() => setReplyTo(comment.id)}
//                     className="h-auto p-0"
//                   >
//                     <ReplyIcon className="h-4 w-4 mr-1" />
//                     Reply
//                   </Button>
//                 )}

//                 {(user?.id === comment.userId ||
//                   user?.role === "admin" ||
//                   user?.role === "supervisor") && (
//                   <>
//                     {editingComment !== comment.id && (
//                       <>
//                         <Button
//                           variant="ghost"
//                           size="sm"
//                           onClick={() => handleToggleResolved(comment)}
//                           className="h-auto p-0"
//                           disabled={updateCommentMutation.isPending}
//                         >
//                           {updateCommentMutation.isPending &&
//                           updateCommentMutation.variables?.id === comment.id ? (
//                             <Loader2 className="h-4 w-4 mr-1 animate-spin" />
//                           ) : (
//                             <CheckCircle2 className="h-4 w-4 mr-1" />
//                           )}
//                           {isResolved ? "Unresolve" : "Resolve"}
//                         </Button>

//                         <Button
//                           variant="ghost"
//                           size="sm"
//                           onClick={() => handleStartEditing(comment)}
//                           className="h-auto p-0"
//                         >
//                           <PencilIcon className="h-4 w-4 mr-1" />
//                           Edit
//                         </Button>

//                         <Button
//                           variant="ghost"
//                           size="sm"
//                           onClick={openDeleteDialog}
//                           className="h-auto p-0 text-red-500 hover:text-red-700"
//                           disabled={deleteCommentMutation.isPending}
//                         >
//                           {deleteCommentMutation.isPending &&
//                           deleteCommentMutation.variables === comment.id ? (
//                             <Loader2 className="h-4 w-4 mr-1 animate-spin" />
//                           ) : (
//                             <TrashIcon className="h-4 w-4 mr-1" />
//                           )}
//                           Delete
//                         </Button>
//                       </>
//                     )}
//                   </>
//                 )}
//               </div>
//             </div>
//           </div>
//           {comment.replies?.map((reply: ExtendedComment) =>
//             renderComment(reply, depth + 1)
//           )}
//         </div>
//       );
//     },
//     [
//       usersMap,
//       user,
//       setReplyTo,
//       handleToggleResolved,
//       handleStartEditing,
//       handleSaveEdit,
//       editingComment,
//       editContent,
//       updateCommentMutation,
//       deleteCommentMutation,
//       setCommentToDelete,
//       setDeleteDialogOpen,
//       setEditingComment,
//     ]
//   );

//   // Loading state
//   if (isLoadingComments || isLoadingUsers) {
//     return (
//       <div className="space-y-4">
//         <div className="space-y-4">
//           {[1, 2, 3].map((i) => (
//             <div key={i} className="flex items-start gap-2">
//               <Skeleton className="h-8 w-8 rounded-full" />
//               <div className="flex-1">
//                 <Skeleton className="h-4 w-32 mb-2" />
//                 <Skeleton className="h-4 w-full mb-2" />
//                 <Skeleton className="h-4 w-3/4" />
//               </div>
//             </div>
//           ))}
//         </div>
//       </div>
//     );
//   }

//   // Error state
//   if (commentsError || usersError) {
//     return (
//       <Alert variant="destructive">
//         <AlertCircle className="h-4 w-4" />
//         <AlertDescription>
//           Failed to load comments. Please try refreshing the page.
//         </AlertDescription>
//       </Alert>
//     );
//   }

//   // Show the reply context if replying to a comment
//   const replyingTo = useMemo(() => {
//     if (!replyTo) return null;
//     const comment = comments.find((c: Comment) => c.id === replyTo);
//     return comment ? usersMap[comment.userId]?.name : null;
//   }, [replyTo, comments, usersMap]);

//   return (
//     <>
//       <div className="space-y-4">
//         <div className="space-y-4">
//           {commentTree.length > 0 ? (
//             commentTree.map((comment) => renderComment(comment))
//           ) : (
//             <p className="text-muted-foreground text-center py-4">
//               No comments yet. Be the first to comment!
//             </p>
//           )}
//         </div>

//         <div className="space-y-2">
//           {replyingTo && (
//             <div className="text-sm text-muted-foreground mb-2">
//               Replying to <span className="font-medium">{replyingTo}</span>
//             </div>
//           )}
//           <Textarea
//             value={newComment}
//             onChange={(e) => setNewComment(e.target.value)}
//             placeholder={replyTo ? "Write a reply..." : "Add a comment..."}
//             className="min-h-[100px]"
//           />
//           <div className="flex justify-end gap-2">
//             {replyTo && (
//               <Button variant="ghost" onClick={() => setReplyTo(null)}>
//                 Cancel Reply
//               </Button>
//             )}
//             <Button
//               onClick={handleSubmitComment}
//               disabled={!newComment.trim() || createCommentMutation.isPending}
//             >
//               {createCommentMutation.isPending && (
//                 <Loader2 className="h-4 w-4 mr-2 animate-spin" />
//               )}
//               {replyTo ? "Reply" : "Comment"}
//             </Button>
//           </div>
//         </div>
//       </div>

//       {/* Delete confirmation dialog */}
//       <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
//         <AlertDialogContent>
//           <AlertDialogHeader>
//             <AlertDialogTitle>
//               Are you sure you want to delete this comment?
//             </AlertDialogTitle>
//             <AlertDialogDescription>
//               This action cannot be undone. This will permanently delete the
//               comment and any replies.
//             </AlertDialogDescription>
//           </AlertDialogHeader>
//           <AlertDialogFooter>
//             <AlertDialogCancel>Cancel</AlertDialogCancel>
//             <AlertDialogAction
//               onClick={() =>
//                 commentToDelete && handleDeleteComment(commentToDelete)
//               }
//               className="bg-red-500 hover:bg-red-600"
//             >
//               Delete
//             </AlertDialogAction>
//           </AlertDialogFooter>
//         </AlertDialogContent>
//       </AlertDialog>
//     </>
//   );
// }

import { useState, useMemo, useCallback, useEffect } from "react";
import { format, formatDistanceToNow } from "date-fns";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/useToast";
import { useComment } from "@/hooks/useComment";
import { useUser } from "@/hooks/useUser";
import { Comment, User } from "@/lib/types";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
} from "../ui/card";
import { ScrollArea } from "../ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  CheckCircle2,
  ReplyIcon,
  TrashIcon,
  Loader2,
  AlertCircle,
  PencilIcon,
  MessageSquare,
} from "lucide-react";

interface ExtendedComment extends Comment {
  parentId?: string;
  resolved?: boolean;
  replies?: ExtendedComment[];
  createdAt?: string | Date;
}

interface CommentThreadProps {
  documentId: string;
  highlightId?: string;
  showThread?: boolean;
  isLoading?: boolean;
  documentStatus?: string;
  projectTeamMembers?: string[];
}

export function CommentThread({
  documentId,
  highlightId,
  showThread = true,
  isLoading: externalLoading = false,
  documentStatus,
  projectTeamMembers = [],
}: CommentThreadProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [replyTo, setReplyTo] = useState<string | null>(null);
  const [newComment, setNewComment] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [commentToDelete, setCommentToDelete] = useState<string | null>(null);
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editContent, setEditContent] = useState("");

  // Check if the current user can create new comments (team member supervisors for documents under review)
  const canCreateComments = useMemo(() => {
    if (!user) return false;

    // Only for documents that are under review
    if (documentStatus !== 'under_review') return false;

    // Only supervisors who are team members can create new comments
    const isTeamMember = projectTeamMembers.includes(user.id);
    const isSupervisor = user.role === 'supervisor';

    return isTeamMember && isSupervisor;
  }, [user, documentStatus, projectTeamMembers]);

  // Check if the current user can reply to comments (team members or admins)
  const canReplyToComments = useMemo(() => {
    if (!user) return false;

    // Team members can reply to comments
    const isTeamMember = projectTeamMembers.includes(user.id);

    // Admins can reply
    const isAdmin = user.role === 'admin';

    return isTeamMember || isAdmin;
  }, [user, projectTeamMembers]);

  // Check if the current user can perform comment actions (edit/delete/resolve)
  const canPerformCommentActions = useCallback((comment: ExtendedComment) => {
    if (!user) return { canEdit: false, canDelete: false, canResolve: false };

    // User can always edit/delete their own comments
    const isOwnComment = user.id === comment.userId;

    // Admins can do everything
    const isAdmin = user.role === 'admin';

    // Team member supervisors can resolve/unresolve comments
    const isTeamMember = projectTeamMembers.includes(user.id);
    const isSupervisor = user.role === 'supervisor';
    const canResolveComments = isTeamMember && isSupervisor;

    return {
      canEdit: isOwnComment || isAdmin,
      canDelete: isOwnComment || isAdmin,
      canResolve: canResolveComments || isAdmin
    };
  }, [user, projectTeamMembers]);

  // Comment hooks
  const {
    create: createCommentMutation,
    update: updateCommentMutation,
    remove: deleteCommentMutation,
    getDocumentComments,
  } = useComment();

  // Get comments for this document
  const {
    data: comments = [],
    isLoading: isLoadingComments,
    error: commentsError,
  } = getDocumentComments(documentId);

  // Extract unique user IDs from comments
  const userIds = useMemo(
    () => Array.from(new Set(comments.map((c: Comment) => c.userId))),
    [comments]
  );

  // Fetch users involved in comments
  const { useUsersByIdsQuery } = useUser();
  const {
    data: users = [],
    isLoading: isLoadingUsers,
    error: usersError,
  } = useUsersByIdsQuery(userIds);

  // Convert users array to a lookup object
  const usersMap = useMemo(() => {
    const map: Record<string, User> = {};
    users.forEach((user) => {
      if (user) {
        map[user.id] = user;
      }
    });
    return map;
  }, [users]);

  // Organize comments into a tree structure
  const commentTree = useMemo(() => {
    const rootComments: ExtendedComment[] = [];
    const commentMap: Record<string, ExtendedComment> = {};

    // First pass: create a map of all comments with empty replies array
    comments.forEach((comment: Comment) => {
      const createdAt = (comment as any).$createdAt || new Date().toISOString();
      commentMap[comment.id] = {
        ...comment,
        replies: [],
        createdAt: createdAt,
      };
    });

    // Second pass: organize into tree structure
    comments.forEach((comment: Comment) => {
      const parentId = comment.parentId;
      if (parentId && commentMap[parentId]) {
        commentMap[parentId].replies!.push(commentMap[comment.id]);
      } else {
        rootComments.push(commentMap[comment.id]);
      }
    });

    // Sort root comments by creation date (newest first)
    return rootComments.sort((a, b) => {
      const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
      const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
      return dateB - dateA;
    });
  }, [comments]);

  // Handle submitting a new comment or reply
  const handleSubmitComment = useCallback(() => {
    // Role-based validation: Check if user can create new comments or reply to existing ones
    const isReply = !!replyTo;
    const hasPermission = isReply ? canReplyToComments : canCreateComments;

    if (!hasPermission) {
      const message = isReply
        ? "You don't have permission to reply to comments."
        : "Only supervisors can add comments to documents under review.";
      toast({
        title: "Permission denied",
        description: message,
        variant: "destructive",
      });
      return;
    }

    if (!user || !newComment.trim()) return;

    const commentData = {
      documentId,
      userId: user.id,
      content: newComment.trim(),
      highlight: highlightId
        ? {
            start: 0,
            end: 0,
            text: "",
          }
        : undefined,
      parentId: replyTo || undefined,
      resolved: false,
    };

    toast({
      title: replyTo ? "Replying to comment..." : "Adding comment...",
      description: "Your comment is being saved.",
    });

    createCommentMutation.mutate(commentData, {
      onSuccess: () => {
        toast({
          title: "Success",
          description: replyTo
            ? "Reply added successfully"
            : "Comment added successfully",
        });
        setNewComment("");
        setReplyTo(null);
      },
      onError: (error) => {
        toast({
          title: "Error",
          description: `Failed to save comment: ${error.message}`,
          variant: "destructive",
        });
      },
    });
  }, [
    canCreateComments,
    canReplyToComments,
    replyTo,
    user,
    newComment,
    documentId,
    highlightId,
    createCommentMutation,
    toast,
  ]);

  // Handle deleting a comment
  const handleDeleteComment = useCallback(
    (commentId: string) => {
      setDeleteDialogOpen(false);

      if (!user) return;

      toast({
        title: "Deleting comment...",
        description: "Your comment is being deleted.",
      });

      deleteCommentMutation.mutate(commentId, {
        onSuccess: () => {
          toast({
            title: "Success",
            description: "Comment deleted successfully",
          });
        },
        onError: (error) => {
          toast({
            title: "Error",
            description: `Failed to delete comment: ${error.message}`,
            variant: "destructive",
          });
        },
      });
    },
    [user, deleteCommentMutation, toast]
  );

  // Handle toggling the resolved status of a comment
  const handleToggleResolved = useCallback(
    (comment: ExtendedComment) => {
      if (!user) return;

      toast({
        title: comment.resolved
          ? "Marking as unresolved..."
          : "Marking as resolved...",
        description: "Updating comment status...",
      });

      updateCommentMutation.mutate(
        {
          id: comment.id,
          updates: {
            resolved: !comment.resolved,
          },
        },
        {
          onSuccess: () => {
            toast({
              title: "Success",
              description: comment.resolved
                ? "Comment marked as unresolved"
                : "Comment marked as resolved",
            });
          },
          onError: (error) => {
            toast({
              title: "Error",
              description: `Failed to update comment: ${error.message}`,
              variant: "destructive",
            });
          },
        }
      );
    },
    [user, updateCommentMutation, toast]
  );

  // Handle editing a comment
  const handleStartEditing = useCallback((comment: ExtendedComment) => {
    setEditingComment(comment.id);
    setEditContent(comment.content);
  }, []);

  const handleSaveEdit = useCallback(
    (comment: ExtendedComment) => {
      if (!user || !editContent.trim()) return;

      toast({
        title: "Updating comment...",
        description: "Your changes are being saved.",
      });

      updateCommentMutation.mutate(
        {
          id: comment.id,
          updates: {
            content: editContent.trim(),
          },
        },
        {
          onSuccess: () => {
            toast({
              title: "Success",
              description: "Comment updated successfully",
            });
            setEditingComment(null);
            setEditContent("");
          },
          onError: (error) => {
            toast({
              title: "Error",
              description: `Failed to update comment: ${error.message}`,
              variant: "destructive",
            });
          },
        }
      );
    },
    [user, editContent, updateCommentMutation, toast]
  );

  // Render a single comment with its replies
  const renderComment = useCallback(
    (comment: ExtendedComment, depth = 0) => {
      const commentUser = usersMap[comment.userId];
      const isResolved = comment.resolved;
      const createdAtDate = comment.createdAt
        ? new Date(comment.createdAt)
        : new Date();
      const timeAgo = formatDistanceToNow(createdAtDate, { addSuffix: true });
      const formattedDate = format(createdAtDate, "PPpp");

      const openDeleteDialog = () => {
        setCommentToDelete(comment.id);
        setDeleteDialogOpen(true);
      };

      return (
        <div
          key={comment.id}
          className={`space-y-3 ${
            depth > 0 ? "ml-8 pl-4 border-l-2 border-muted" : ""
          } ${isResolved ? "opacity-80" : ""}`}
        >
          <div className="flex items-start gap-3">
            <Avatar className="h-9 w-9">
              {commentUser && (
                <>
                  <AvatarImage
                    src={commentUser.profileImage}
                    alt={commentUser.name}
                  />
                  <AvatarFallback>
                    {commentUser.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </>
              )}
              {!commentUser && <AvatarFallback>?</AvatarFallback>}
            </Avatar>
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-2 flex-wrap">
                <span className="font-medium">
                  {commentUser ? commentUser.name : "Unknown User"}
                </span>
                <span
                  className="text-sm text-muted-foreground"
                  title={formattedDate}
                >
                  {timeAgo}
                </span>
                {isResolved && (
                  <Badge
                    variant="outline"
                    className="bg-green-50 text-green-700 border-green-200 text-xs"
                  >
                    <CheckCircle2 className="h-3 w-3 mr-1" />
                    Resolved
                  </Badge>
                )}
              </div>

              {comment.highlight && (
                <div className="text-sm bg-muted p-2 rounded border">
                  <div className="flex justify-between items-start mb-1">
                    <span className="font-medium text-xs text-muted-foreground">
                      Selected text:
                      {comment.highlight.page && (
                        <span className="ml-1">Page {comment.highlight.page}</span>
                      )}
                    </span>
                  </div>
                  &quot;{comment.highlight.text}&quot;
                </div>
              )}

              {editingComment === comment.id ? (
                <div className="mt-1">
                  <Textarea
                    value={editContent}
                    onChange={(e) => setEditContent(e.target.value)}
                    className="min-h-[80px] mb-2"
                  />
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingComment(null)}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handleSaveEdit(comment)}
                      disabled={
                        !editContent.trim() || updateCommentMutation.isPending
                      }
                    >
                      {updateCommentMutation.isPending &&
                      updateCommentMutation.variables?.id === comment.id ? (
                        <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                      ) : (
                        "Save"
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                <p className="text-sm">{comment.content}</p>
              )}

              <div className="flex items-center gap-3 text-sm">
                {editingComment !== comment.id && canReplyToComments && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setReplyTo(comment.id)}
                    className="h-auto px-0 text-muted-foreground"
                  >
                    <ReplyIcon className="h-4 w-4 mr-1.5" />
                    Reply
                  </Button>
                )}

                {(() => {
                  const permissions = canPerformCommentActions(comment);
                  const hasAnyPermission = permissions.canEdit || permissions.canDelete || permissions.canResolve;

                  return hasAnyPermission && (
                    <>
                      {editingComment !== comment.id && (
                        <>
                          {permissions.canResolve && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleToggleResolved(comment)}
                              className="h-auto px-0 text-muted-foreground"
                              disabled={updateCommentMutation.isPending}
                            >
                              {updateCommentMutation.isPending &&
                              updateCommentMutation.variables?.id === comment.id ? (
                                <Loader2 className="h-4 w-4 mr-1.5 animate-spin" />
                              ) : (
                                <CheckCircle2 className="h-4 w-4 mr-1.5" />
                              )}
                              {isResolved ? "Unresolve" : "Resolve"}
                            </Button>
                          )}

                          {permissions.canEdit && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleStartEditing(comment)}
                              className="h-auto px-0 text-muted-foreground"
                            >
                              <PencilIcon className="h-4 w-4 mr-1.5" />
                              Edit
                            </Button>
                          )}

                          {permissions.canDelete && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={openDeleteDialog}
                              className="h-auto px-0 text-red-500 hover:text-red-700"
                              disabled={deleteCommentMutation.isPending}
                            >
                              {deleteCommentMutation.isPending &&
                              deleteCommentMutation.variables === comment.id ? (
                                <Loader2 className="h-4 w-4 mr-1.5 animate-spin" />
                              ) : (
                                <TrashIcon className="h-4 w-4 mr-1.5" />
                              )}
                              Delete
                            </Button>
                          )}
                        </>
                      )}
                    </>
                  );
                })()}
              </div>
            </div>
          </div>
          {comment.replies?.map((reply: ExtendedComment) =>
            renderComment(reply, depth + 1)
          )}
        </div>
      );
    },
    [
      usersMap,
      user,
      canReplyToComments,
      canPerformCommentActions,
      editingComment,
      editContent,
      updateCommentMutation,
      deleteCommentMutation,
      handleSaveEdit,
      handleToggleResolved,
      handleStartEditing,
    ]
  );

  // Show the reply context if replying to a comment
  const replyingTo = useMemo(() => {
    if (!replyTo) return null;
    const comment = comments.find((c: Comment) => c.id === replyTo);
    return comment ? usersMap[comment.userId]?.name : null;
  }, [replyTo, comments, usersMap]);

  // Loading state
  if (externalLoading || isLoadingComments || isLoadingUsers) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Comments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Skeleton className="w-8 h-8 rounded-full" />
                  <div>
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-3 w-32 mt-1" />
                  </div>
                </div>
                <Skeleton className="h-16 w-full" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (commentsError || usersError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Comments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load comments. Please try refreshing the page.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Comments ({comments.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[calc(100vh-16rem)]">
          <div className="space-y-6 pr-4">
            {commentTree.length > 0 ? (
              commentTree.map((comment) => renderComment(comment))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <MessageSquare className="mx-auto h-8 w-8 mb-2" />
                <p className="font-medium">No comments yet</p>
                <p className="text-sm mt-1">
                  Supervisors can add comments. Document owners and team members can reply.
                </p>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
      {showThread && (canCreateComments || canReplyToComments) && (
        <CardFooter className="border-t pt-4">
          <div className="w-full space-y-3">
            {replyingTo && (
              <div className="text-sm text-muted-foreground">
                Replying to <span className="font-medium">{replyingTo}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="ml-2 h-auto p-0"
                  onClick={() => setReplyTo(null)}
                >
                  Cancel
                </Button>
              </div>
            )}
            <Textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder={replyTo ? "Write a reply..." : "Add a comment..."}
              className="min-h-[100px]"
            />
            <div className="flex justify-end">
              <Button
                onClick={handleSubmitComment}
                disabled={!newComment.trim() || createCommentMutation.isPending}
              >
                {createCommentMutation.isPending && (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                )}
                {replyTo ? "Reply" : "Post Comment"}
              </Button>
            </div>
          </div>
        </CardFooter>
      )}

      {/* Delete confirmation dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Are you sure you want to delete this comment?
            </AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              comment and any replies.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() =>
                commentToDelete && handleDeleteComment(commentToDelete)
              }
              className="bg-red-500 hover:bg-red-600"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}

