"use client";

import { format } from "date-fns";
import { User } from "@/lib/types";
import { SearchBar } from "@/components/search/SearchBar";
import { NotificationsPanel } from "@/components/notifications/NotificationsPanel";
import { Notification } from "@/lib/types";
import { Skeleton } from "@/components/ui/skeleton";
import { memo } from "react";

interface DashboardHeaderProps {
  currentUser?: User | null;
  notifications: Notification[];
  isLoadingNotifications: boolean;
  onMarkAsRead: (id: string) => void;
  onMarkAllAsRead?: () => void;
  onDelete?: (id: string) => void;
  onDeleteAll?: () => void;
}

export const DashboardHeader = memo(function DashboardHeader({
  currentUser,
  notifications,
  isLoadingNotifications,
  onMarkAsRead,
  onMarkAllAsRead,
  onDelete,
  onDeleteAll,
}: DashboardHeaderProps) {
  return (
    <div className="flex flex-col md:flex-row md:items-center justify-between gap-6 mb-8">
      <div>
        {currentUser ? (
          <>
            <h1 className="dashboard-title">
              Welcome back, {currentUser.name}
            </h1>
            <p className="dashboard-subtitle">
              {format(new Date(), "EEEE, MMMM d, yyyy")}
            </p>
          </>
        ) : (
          <>
            <Skeleton className="h-9 w-64 mb-2" />
            <Skeleton className="h-5 w-48" />
          </>
        )}
      </div>
      <div className="flex items-center gap-4">
        <SearchBar />
        <NotificationsPanel
          notifications={notifications}
          isLoading={isLoadingNotifications}
          onMarkAsRead={onMarkAsRead}
          onMarkAllAsRead={onMarkAllAsRead}
          onDelete={onDelete}
          onDeleteAll={onDeleteAll}
        />
      </div>
    </div>
  );
});
