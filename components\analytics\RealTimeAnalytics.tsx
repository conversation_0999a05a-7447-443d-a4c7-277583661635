/**
 * Real-Time Analytics Component
 *
 * This component uses real data from the useAnalytics hook to display comprehensive
 * analytics including projects, documents, milestones, users, and notifications.
 *
 * Key improvements:
 * - Uses real data from hooks instead of mock data
 * - Proper error handling and loading states
 * - Color-coded metrics based on performance thresholds
 * - Enhanced data visualization with status breakdowns
 * - Responsive design with empty state handling
 * - Role-based analytics filtering
 */

import React, { useState } from 'react';
import { useAnalytics } from '@/hooks/useAnalytics';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>
} from 'recharts';
import {
  RefreshCw, Download, TrendingUp, TrendingDown, Users, FileText,
  CheckCircle, Clock, AlertTriangle, Calendar
} from 'lucide-react';
import { TimeRange } from '@/lib/utils/analytics';

const COLORS = {
  primary: '#3b82f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  info: '#06b6d4',
  secondary: '#6b7280'
};

interface RealTimeAnalyticsProps {
  /** Initial time range for analytics */
  initialTimeRange?: TimeRange;
  /** Enable extended analytics features */
  enableExtendedAnalytics?: boolean;
  /** Custom filters */
  filters?: {
    projectStatus?: string;
    userRole?: string;
    searchTerm?: string;
  };
}

export function RealTimeAnalytics({
  initialTimeRange = 'month',
  enableExtendedAnalytics = true,
  filters = {}
}: RealTimeAnalyticsProps) {
  const [selectedTimeRange, setSelectedTimeRange] = useState<TimeRange>(initialTimeRange);
  
  const {
    analytics,
    isLoading,
    error,
    isFetching,
    isRefetching,
    lastUpdated,
    refreshAnalytics,
    exportAnalytics,
    setTimeRange
  } = useAnalytics({
    timeRange: selectedTimeRange,
    enableRealTime: true,
    filters,
    includeExtendedAnalytics: enableExtendedAnalytics
  });

  // Handle time range change
  const handleTimeRangeChange = (newRange: TimeRange) => {
    setSelectedTimeRange(newRange);
    setTimeRange(newRange);
  };

  // Handle refresh
  const handleRefresh = async () => {
    await refreshAnalytics();
  };

  // Handle export
  const handleExport = (format: 'csv' | 'json') => {
    exportAnalytics(format);
  };

  if (isLoading) {
    return <AnalyticsLoadingSkeleton />;
  }

  if (error) {
    return (
      <Card className="border-red-200">
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p className="font-medium">Failed to load analytics</p>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
            <Button onClick={handleRefresh} className="mt-4" variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!analytics) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <FileText className="h-8 w-8 mx-auto mb-2" />
            <p>No data available for analytics</p>
            <p className="text-sm mt-1">Create some projects to see analytics</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Real-Time Analytics</h2>
          <p className="text-muted-foreground">
            Live insights from your project data
            {lastUpdated && (
              <span className="ml-2 text-xs">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Time Range Selector */}
          <Tabs value={selectedTimeRange} onValueChange={handleTimeRangeChange as any}>
            <TabsList>
              <TabsTrigger value="month">Month</TabsTrigger>
              <TabsTrigger value="quarter">Quarter</TabsTrigger>
              <TabsTrigger value="year">Year</TabsTrigger>
            </TabsList>
          </Tabs>
          
          {/* Action Buttons */}
          <Button
            onClick={handleRefresh}
            disabled={isRefetching}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefetching ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button
            onClick={() => handleExport('json')}
            variant="outline"
            size="sm"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Projects"
          value={analytics.totalProjects}
          icon={<FileText className="h-4 w-4" />}
          trend={analytics.projectTrends.length > 1 ? 
            analytics.projectTrends[analytics.projectTrends.length - 1].newProjects - 
            analytics.projectTrends[analytics.projectTrends.length - 2].newProjects : 0}
        />
        
        <MetricCard
          title="Active Users"
          value={analytics.userEngagement.activeUsers}
          icon={<Users className="h-4 w-4" />}
          total={analytics.userEngagement.totalUsers}
        />
        
        <MetricCard
          title="Completion Rate"
          value={`${analytics.totalProjects > 0 ? Math.round((analytics.projectsByStatus.completed / analytics.totalProjects) * 100) : 0}%`}
          icon={<CheckCircle className="h-4 w-4" />}
          color="success"
        />
        
        <MetricCard
          title="Avg. Completion Time"
          value={`${analytics.averageCompletionTime > 0 ? Math.round(analytics.averageCompletionTime) : 'N/A'} ${analytics.averageCompletionTime > 0 ? 'days' : ''}`}
          icon={<Clock className="h-4 w-4" />}
        />
      </div>

      {/* Extended Analytics */}
      {enableExtendedAnalytics && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <MetricCard
            title="Total Documents"
            value={analytics.documentAnalytics.totalDocuments}
            icon={<FileText className="h-4 w-4" />}
            subtitle={`${analytics.documentAnalytics.approvalRate}% approval rate`}
            color={analytics.documentAnalytics.approvalRate >= 80 ? 'success' : analytics.documentAnalytics.approvalRate >= 60 ? 'warning' : 'danger'}
          />
          
          <MetricCard
            title="Total Milestones"
            value={analytics.milestoneAnalytics.totalMilestones}
            icon={<Calendar className="h-4 w-4" />}
            subtitle={`${analytics.milestoneAnalytics.overdueMilestones} overdue`}
            color={analytics.milestoneAnalytics.overdueMilestones > 0 ? 'warning' : 'default'}
          />
          
          <MetricCard
            title="Notifications"
            value={analytics.notificationAnalytics.totalNotifications}
            icon={<AlertTriangle className="h-4 w-4" />}
            subtitle={`${analytics.notificationAnalytics.unreadNotifications} unread`}
            color={analytics.notificationAnalytics.unreadNotifications > 0 ? 'danger' : 'default'}
          />
        </div>
      )}

      {/* Charts */}
      <Tabs defaultValue="projects" className="space-y-4">
        <TabsList>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="status">Status Distribution</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          {enableExtendedAnalytics && (
            <>
              <TabsTrigger value="documents">Documents</TabsTrigger>
              <TabsTrigger value="users">Users</TabsTrigger>
            </>
          )}
        </TabsList>

        <TabsContent value="projects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Projects by Month</CardTitle>
              <CardDescription>Project creation and completion trends</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                {analytics.projectsByMonth && analytics.projectsByMonth.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={analytics.projectsByMonth}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="shortMonth" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="count" name="Created" fill={COLORS.primary} />
                      <Bar dataKey="completed" name="Completed" fill={COLORS.success} />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    <div className="text-center">
                      <FileText className="h-8 w-8 mx-auto mb-2" />
                      <p>No project data available for the selected time range</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="status" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Project Status Distribution</CardTitle>
              <CardDescription>Current status of all projects</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                {analytics.totalProjects > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={Object.entries(analytics.projectsByStatus)
                          .filter(([, count]) => count > 0)
                          .map(([status, count]) => ({
                            name: status.replace('_', ' ').toUpperCase(),
                            value: count
                          }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {Object.entries(analytics.projectsByStatus)
                          .filter(([, count]) => count > 0)
                          .map((_, index) => (
                            <Cell key={`cell-${index}`} fill={Object.values(COLORS)[index % Object.values(COLORS).length]} />
                          ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    <div className="text-center">
                      <FileText className="h-8 w-8 mx-auto mb-2" />
                      <p>No projects available to display status distribution</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Project Trends</CardTitle>
              <CardDescription>New vs completed projects over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                {analytics.projectTrends && analytics.projectTrends.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={analytics.projectTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="period" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="newProjects"
                        stroke={COLORS.primary}
                        name="New Projects"
                        strokeWidth={2}
                        dot={{ fill: COLORS.primary, strokeWidth: 2, r: 4 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="completedProjects"
                        stroke={COLORS.success}
                        name="Completed Projects"
                        strokeWidth={2}
                        dot={{ fill: COLORS.success, strokeWidth: 2, r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    <div className="text-center">
                      <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                      <p>No trend data available for the selected time range</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {enableExtendedAnalytics && (
          <>
            <TabsContent value="documents" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Document Analytics</CardTitle>
                  <CardDescription>Document approval and status trends</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center">
                      <div className={`text-2xl font-bold ${
                        analytics.documentAnalytics.approvalRate >= 80 ? 'text-green-600' :
                        analytics.documentAnalytics.approvalRate >= 60 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {analytics.documentAnalytics.approvalRate}%
                      </div>
                      <div className="text-sm text-muted-foreground">Approval Rate</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {analytics.documentAnalytics.averageDocumentsPerProject.toFixed(1)}
                      </div>
                      <div className="text-sm text-muted-foreground">Docs per Project</div>
                    </div>
                  </div>

                  {/* Document Status Breakdown */}
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div className="text-center p-2 bg-green-50 rounded">
                      <div className="font-semibold text-green-700">
                        {analytics.documentAnalytics.documentsByStatus.approved || 0}
                      </div>
                      <div className="text-green-600">Approved</div>
                    </div>
                    <div className="text-center p-2 bg-yellow-50 rounded">
                      <div className="font-semibold text-yellow-700">
                        {analytics.documentAnalytics.documentsByStatus.under_review || 0}
                      </div>
                      <div className="text-yellow-600">Under Review</div>
                    </div>
                    <div className="text-center p-2 bg-red-50 rounded">
                      <div className="font-semibold text-red-700">
                        {analytics.documentAnalytics.documentsByStatus.rejected || 0}
                      </div>
                      <div className="text-red-600">Rejected</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="users" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>User Engagement</CardTitle>
                  <CardDescription>User activity and engagement metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center">
                      <div className={`text-2xl font-bold ${
                        analytics.userEngagement.engagementRate >= 70 ? 'text-blue-600' :
                        analytics.userEngagement.engagementRate >= 50 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {analytics.userEngagement.engagementRate}%
                      </div>
                      <div className="text-sm text-muted-foreground">Engagement Rate</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {analytics.userEngagement.averageProjectsPerUser.toFixed(1)}
                      </div>
                      <div className="text-sm text-muted-foreground">Projects per User</div>
                    </div>
                  </div>

                  {/* User Role Breakdown */}
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div className="text-center p-2 bg-blue-50 rounded">
                      <div className="font-semibold text-blue-700">
                        {analytics.userEngagement.usersByRole.student || 0}
                      </div>
                      <div className="text-blue-600">Students</div>
                    </div>
                    <div className="text-center p-2 bg-purple-50 rounded">
                      <div className="font-semibold text-purple-700">
                        {analytics.userEngagement.usersByRole.supervisor || 0}
                      </div>
                      <div className="text-purple-600">Supervisors</div>
                    </div>
                    <div className="text-center p-2 bg-gray-50 rounded">
                      <div className="font-semibold text-gray-700">
                        {analytics.userEngagement.usersByRole.manager || 0}
                      </div>
                      <div className="text-gray-600">Managers</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </>
        )}
      </Tabs>

      {/* Data Freshness Indicator */}
      {isFetching && (
        <div className="flex items-center justify-center py-4">
          <RefreshCw className="h-4 w-4 animate-spin mr-2" />
          <span className="text-sm text-muted-foreground">Updating analytics...</span>
        </div>
      )}
    </div>
  );
}

// Helper Components
interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: number;
  total?: number;
  subtitle?: string;
  color?: 'default' | 'success' | 'warning' | 'danger';
}

function MetricCard({ title, value, icon, trend, total, subtitle, color = 'default' }: MetricCardProps) {
  const colorClasses = {
    default: 'text-foreground',
    success: 'text-green-600',
    warning: 'text-yellow-600',
    danger: 'text-red-600'
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {icon}
            <span className="text-sm font-medium text-muted-foreground">{title}</span>
          </div>
          {trend !== undefined && (
            <Badge variant={trend >= 0 ? 'default' : 'destructive'}>
              {trend >= 0 ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
              {Math.abs(trend)}
            </Badge>
          )}
        </div>
        <div className="mt-2">
          <div className={`text-2xl font-bold ${colorClasses[color]}`}>
            {value}
            {total && <span className="text-sm text-muted-foreground ml-1">/ {total}</span>}
          </div>
          {subtitle && (
            <p className="text-xs text-muted-foreground mt-1">{subtitle}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function AnalyticsLoadingSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-64 mt-2" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-9 w-24" />
          <Skeleton className="h-9 w-20" />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <Skeleton className="h-4 w-24 mb-2" />
              <Skeleton className="h-8 w-16" />
            </CardContent>
          </Card>
        ))}
      </div>
      
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-48" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    </div>
  );
}
