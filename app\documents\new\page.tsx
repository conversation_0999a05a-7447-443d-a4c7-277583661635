'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DocumentType, Project } from '@/lib/types';
import { Upload, Save, AlertCircle, FileText } from 'lucide-react';
import { useState, useCallback, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { DocumentEditor } from '@/components/documents/DocumentEditor';
import { useAuth } from '@/hooks/useAuth';
import { useProject } from '@/hooks/useProject';
import { useToast } from '@/hooks/useToast';
import { useDocument } from '@/hooks/useDocument';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';


const DOCUMENT_TYPES = [
  { value: 'concept', label: 'Concept Paper' },
  { value: 'proposal', label: 'Thesis Proposal' },
  { value: 'thesis', label: 'Thesis' },
] as const;

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ACCEPTED_FILE_TYPES = '.pdf'; // Only accept PDF files

interface DocumentForm {
  title: string;
  type: DocumentType;
  content: string;
  file: File | null;
  projectId: string;
}

interface FormValidation {
  title: boolean;
  type: boolean;
  projectId: boolean;
  file: boolean;
  content: boolean;
  isValid: boolean;
}

export default function NewDocumentPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const { toast } = useToast();
  const { useProjectsQuery } = useProject();
  const { upload, create } = useDocument();

  // Redirect if not a student
  useEffect(() => {
    if (user && user.role !== 'student') {
      toast({
        variant: "destructive",
        title: "Access Denied",
        description: "Only students can create documents.",
      });
      router.push('/dashboard');
    }
  }, [user, router, toast]);

  // Form state
  const [form, setForm] = useState<DocumentForm>({
    title: '',
    type: 'concept',
    content: '',
    file: null,
    projectId: searchParams.get('projectId') || '',
  });

  // UI state
  const [wordCount, setWordCount] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [formErrors, setFormErrors] = useState<FormValidation>({
    title: true,
    type: true,
    projectId: true,
    file: true,
    content: true,
    isValid: true
  });
  const [showValidationErrors, setShowValidationErrors] = useState(false);

  // Fetch user's projects
  const { data: projects = [], isLoading: isLoadingProjects } = useProjectsQuery({
    // @ts-ignore - The select property is supported by react-query but TypeScript doesn't recognize it
    select: (data: Project[]) => data.filter((project: Project) =>
      project.studentId === user?.id ||
      (project.supervisorIds && project.supervisorIds.includes(user?.id || ''))
    )
  });

  // Validate form fields
  const validateForm = useCallback(() => {
    const errors: FormValidation = {
      title: !!form.title.trim(),
      type: !!form.type,
      projectId: !!form.projectId,
      file: true, // File is optional if content is provided
      content: true, // Content is optional if file is provided
      isValid: false
    };

    // For upload tab, require file
    // For write tab, require content
    if (form.file) {
      errors.file = true;
    } else if (form.content) {
      errors.content = true;
    } else {
      // If neither file nor content is provided, mark both as invalid
      errors.file = false;
      errors.content = false;
    }

    // Check if form is valid overall
    errors.isValid = errors.title && errors.type && errors.projectId &&
                    (errors.file || errors.content);

    setFormErrors(errors);
    return errors.isValid;
  }, [form]);

  const handleSubmit = useCallback(async (e: React.FormEvent, saveAsDraft = false) => {
    e.preventDefault();
    setShowValidationErrors(true);

    // For drafts, only require title and project
    const isDraftValid = form.title.trim() && form.projectId;
    const isSubmissionValid = validateForm();

    if (saveAsDraft && !isDraftValid) {
      toast({
        title: "Validation Error",
        description: "Please provide at least a title and select a project to save as draft",
        variant: "destructive"
      });
      return;
    }

    if (!saveAsDraft && !isSubmissionValid) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive"
      });
      return;
    }

    // Validate user is logged in
    if (!user?.id) {
      toast({
        title: "Error",
        description: "You must be logged in to create a document",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSubmitting(true);
      setUploadProgress(0);

      const documentStatus = saveAsDraft ? 'draft' : 'under_review';

      if (form.file) {
        // Use the upload mutation for file uploads
        await upload.mutateAsync({
          file: form.file,
          metadata: {
            type: form.type,
            title: form.title,
            projectId: form.projectId,
            studentId: user?.id || '',
            supervisorIds: projects.find(p => p.id === form.projectId)?.supervisorIds || [],
            status: documentStatus,
          },
          onProgress: (progress) => {
            setUploadProgress(progress * 100);
          }
        });
      } else {
        // Create document with content
        await create.mutateAsync({
          document: {
            title: form.title,
            type: form.type,
            content: form.content || '', // Ensure content is never null
            status: documentStatus,
            projectId: form.projectId,
            studentId: user?.id || '',
            supervisorIds: projects.find(p => p.id === form.projectId)?.supervisorIds || [],
            fileUrl: undefined, // Add fileUrl field even if undefined
            comments: [],
            reviewDue: new Date(),
            lastModified: new Date()
          }
        });
      }

      toast({
        title: "Success",
        description: saveAsDraft ? "Document saved as draft successfully" : "Document submitted successfully"
      });

      router.push('/documents');
    } catch (error) {
      console.error('Error saving document:', error);

      // Extract more detailed error information
      let errorMessage = "Failed to save document";
      let errorDetails = "";

      if (error instanceof Error) {
        errorMessage = error.message;

        // For Appwrite errors
        const appwriteCode = (error as any).code;
        const appwriteResponse = (error as any).response;

        if (appwriteCode) {
          errorDetails = `Error code: ${appwriteCode}`;
        }

        console.error('Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack,
          code: appwriteCode,
          response: appwriteResponse
        });
      }

      toast({
        title: "Error",
        description: errorMessage + (errorDetails ? ` (${errorDetails})` : ""),
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [form, router, toast, user, projects, upload, create]);

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      toast({
        title: "Error",
        description: "File size exceeds 10MB limit",
        variant: "destructive"
      });
      return;
    }

    // Validate file type is PDF
    const fileType = file.type.toLowerCase();
    const fileExtension = file.name.split('.').pop()?.toLowerCase();

    if (fileType !== 'application/pdf' && fileExtension !== 'pdf') {
      toast({
        title: "Error",
        description: "Only PDF files are accepted",
        variant: "destructive"
      });
      return;
    }

    setForm(prev => ({ ...prev, file }));

    // Validate form after file change
    setTimeout(() => validateForm(), 0);
  }, [toast, validateForm]);

  const updateFormField = useCallback(<K extends keyof DocumentForm>(
    field: K,
    value: DocumentForm[K]
  ) => {
    setForm(prev => ({ ...prev, [field]: value }));

    // Validate form after field update (with slight delay to ensure state is updated)
    setTimeout(() => validateForm(), 0);
  }, [validateForm]);

  return (
    <div className="min-h-screen bg-background">

      <main className="container py-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold tracking-tight">Create New Document</h1>
          <p className="text-muted-foreground">Write or upload your document</p>
        </div>

        {/* Draft info */}
        <Alert className="mb-6 border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
          <FileText className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          <AlertTitle className="text-blue-800 dark:text-blue-200">Save as Draft</AlertTitle>
          <AlertDescription className="text-blue-700 dark:text-blue-300">
            You can save your document as a draft and continue editing it multiple times before submitting for review.
            Drafts only require a title and project selection, so you can start writing and save your progress as you go.
          </AlertDescription>
        </Alert>

        {/* Show validation alert if there are errors */}
        {showValidationErrors && !formErrors.isValid && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Validation Error</AlertTitle>
            <AlertDescription>
              Please fix the following errors:
              <ul className="list-disc pl-5 mt-2">
                {!formErrors.title && <li>Document title is required</li>}
                {!formErrors.type && <li>Document type is required</li>}
                {!formErrors.projectId && <li>Project selection is required</li>}
                {!formErrors.file && !formErrors.content && <li>Either upload a file or write document content</li>}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Document Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="title" className={!formErrors.title && showValidationErrors ? "text-destructive" : ""}>
                    Document Title <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="title"
                    value={form.title}
                    onChange={e => updateFormField('title', e.target.value)}
                    placeholder="Enter document title"
                    required
                    className={!formErrors.title && showValidationErrors ? "border-destructive" : ""}
                    aria-invalid={!formErrors.title && showValidationErrors}
                    disabled={isSubmitting}
                  />
                  {!formErrors.title && showValidationErrors && (
                    <p className="text-sm text-destructive">Title is required</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label className={!formErrors.type && showValidationErrors ? "text-destructive" : ""}>
                    Document Type <span className="text-destructive">*</span>
                  </Label>
                  <Select
                    value={form.type}
                    onValueChange={value => updateFormField('type', value as DocumentType)}
                    disabled={isSubmitting}
                  >
                    <SelectTrigger className={!formErrors.type && showValidationErrors ? "border-destructive" : ""}>
                      <SelectValue placeholder="Select document type" />
                    </SelectTrigger>
                    <SelectContent>
                      {DOCUMENT_TYPES.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {!formErrors.type && showValidationErrors && (
                    <p className="text-sm text-destructive">Document type is required</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label className={!formErrors.projectId && showValidationErrors ? "text-destructive" : ""}>
                    Project <span className="text-destructive">*</span>
                  </Label>
                  <Select
                    value={form.projectId}
                    onValueChange={value => updateFormField('projectId', value)}
                    disabled={isSubmitting}
                  >
                    <SelectTrigger className={!formErrors.projectId && showValidationErrors ? "border-destructive" : ""}>
                      <SelectValue placeholder="Select a project" />
                    </SelectTrigger>
                    <SelectContent>
                      {isLoadingProjects ? (
                        <SelectItem value="loading" disabled>Loading projects...</SelectItem>
                      ) : projects.length > 0 ? (
                        projects.map(project => (
                          <SelectItem key={project.id} value={project.id}>
                            {project.title}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="none" disabled>No projects available</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  {!formErrors.projectId && showValidationErrors && (
                    <p className="text-sm text-destructive">Project selection is required</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="write" className="w-full">
            <TabsList>
              <TabsTrigger value="write" disabled={isSubmitting}>Write Document</TabsTrigger>
              <TabsTrigger value="upload" disabled={isSubmitting}>Upload File</TabsTrigger>
            </TabsList>

            <TabsContent value="write">
              <Card>
                <CardContent className="p-6">
                  {!formErrors.content && !formErrors.file && showValidationErrors && (
                    <Alert variant="destructive" className="mb-4">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Content Required</AlertTitle>
                      <AlertDescription>
                        Please write some content or upload a file
                      </AlertDescription>
                    </Alert>
                  )}

                  <DocumentEditor
                    content={form.content}
                    onUpdate={content => updateFormField('content', content)}
                    onWordCountChange={setWordCount}
                    editable={!isSubmitting}
                  />
                  <div className="mt-2 text-sm text-muted-foreground text-right">
                    {wordCount} words
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="upload">
              <Card>
                <CardContent className="p-6">
                  <div className={`border-2 border-dashed rounded-lg p-8 ${!formErrors.file && !formErrors.content && showValidationErrors ? "border-destructive" : ""}`}>
                    <div className="flex flex-col items-center justify-center space-y-4">
                      <Upload className={`h-8 w-8 ${!formErrors.file && !formErrors.content && showValidationErrors ? "text-destructive" : "text-muted-foreground"}`} />
                      <div className="text-center">
                        <p className="text-sm text-muted-foreground">
                          Upload your document in PDF format only
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Maximum file size: 10MB
                        </p>
                      </div>

                      {/* File input with drag and drop support */}
                      <div className="w-full max-w-md">
                        <Input
                          type="file"
                          accept={ACCEPTED_FILE_TYPES}
                          onChange={handleFileChange}
                          className="max-w-xs"
                          disabled={isSubmitting}
                          aria-invalid={!formErrors.file && !formErrors.content && showValidationErrors}
                        />
                        {!formErrors.file && !formErrors.content && showValidationErrors && (
                          <p className="text-sm text-destructive mt-2">Please upload a PDF file</p>
                        )}
                      </div>

                      {form.file && (
                        <div className="flex items-center space-x-2">
                          <FileText className="h-5 w-5 text-primary" />
                          <p className="text-sm">
                            Selected file: <span className="font-medium">{form.file.name}</span>
                          </p>
                        </div>
                      )}

                      {/* Show upload progress when submitting */}
                      {isSubmitting && form.file && (
                        <div className="w-full max-w-md">
                          <p className="text-sm mb-2">Uploading: {Math.round(uploadProgress)}%</p>
                          <Progress value={uploadProgress} className="h-2" />
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="secondary"
              onClick={e => handleSubmit(e, true)}
              disabled={isSubmitting || (!form.title.trim() || !form.projectId)}
              title={(!form.title.trim() || !form.projectId) ? "Please provide at least a title and select a project to save as draft" : "Save as draft - you can edit and save multiple times before submitting"}
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save as Draft
                </>
              )}
            </Button>
            <Button
              type="submit"
              variant="default"
              onClick={e => handleSubmit(e)}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {form.file ? "Uploading..." : "Submitting..."}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Submit Document
                </>
              )}
            </Button>
          </div>
        </form>
      </main>
    </div>
  );
}





