'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Plus } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';

// Components
import { Button } from '@/components/ui/button';
import { Header } from '@/components/layout/Header';
import { ProjectList } from '@/components/dashboard/ProjectList';

// Hooks
import { useAuth } from '@/hooks/useAuth';
import { useProject } from '@/hooks/useProject';
import { useUser } from '@/hooks/useUser';

// API
import { getUsersByRole } from '@/lib/api/users';

// Types
import { Project, User, ProjectStatus } from '@/lib/types';

type SortField = "title" | "lastActivity" | "createdAt";

export default function ProjectsPage() {
  const { user } = useAuth();
  const router = useRouter();
  const { useProjectsQuery } = useProject();
  const { useUsersQuery } = useUser();

  // UI state - matching desktop projects tab exactly
  const [sortBy, setSortBy] = useState<SortField>("lastActivity");
  const [filterStatus, setFilterStatus] = useState<ProjectStatus | "all">("all");

  // Data queries
  const { data: projects = [], isLoading: projectsLoading } = useProjectsQuery();
  const { data: users = [] } = useUsersQuery();

  // Get supervisors for the ProjectList component
  const { data: supervisors = [] } = useQuery({
    queryKey: ['users', 'supervisor'],
    queryFn: () => getUsersByRole('supervisor')
  });

  // Filter projects based on user role (same logic as original)
  const userFilteredProjects = projects?.filter((project: Project) => {
    return user?.role === 'student'
      ? project.studentId === user.id
      : user?.role === 'supervisor'
        ? project.supervisorIds.includes(user.id)
        : true; // Manager can see all projects
  });

  // Handle sort and filter changes
  const handleSortChange = (value: SortField) => {
    setSortBy(value);
  };

  const handleFilterChange = (value: ProjectStatus | "all") => {
    setFilterStatus(value);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="container py-8 px-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h1 className="dashboard-title">Projects</h1>
              <p className="dashboard-subtitle">
                Manage and track all your projects
              </p>
            </div>
            <button
              className="dashboard-button-primary"
              onClick={() => router.push('/projects/new')}
            >
              <Plus className="mr-2 h-4 w-4" /> New Project
            </button>
          </div>
          <ProjectList
            isLoading={projectsLoading}
            projects={userFilteredProjects as Project[]}
            users={users as User[]}
            supervisors={supervisors}
            sortBy={sortBy}
            filterStatus={filterStatus}
            onSortChange={handleSortChange}
            onFilterChange={handleFilterChange}
          />
        </div>
      </div>
    </div>
  );
}