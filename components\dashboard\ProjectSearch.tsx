import { memo } from 'react';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select } from '@/components/ui/select';
import { ProjectStatus, User } from '@/lib/types';

interface ProjectFilters {
  search: string;
  status: ProjectStatus | 'all';
  sortBy: 'createdAt' | 'title' | 'lastActivity';
  supervisorId: string;
}

interface ProjectSearchProps {
  filters: ProjectFilters;
  onFiltersChange: (filters: Partial<ProjectFilters>) => void;
  supervisors: User[];
}

export const ProjectSearch = memo(function ProjectSearch({
  filters,
  onFiltersChange,
  supervisors
}: ProjectSearchProps) {
  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'active', label: 'Active' },
    { value: 'completed', label: 'Completed' },
    { value: 'on_hold', label: 'On Hold' },
  ];

  return (
    <div className="flex flex-col gap-3 md:flex-row md:items-center">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search projects..."
          value={filters.search}
          onChange={(e) => onFiltersChange({ search: e.target.value })}
          className="pl-9"
          data-testid="project-search-input"
        />
      </div>
      
      <div className="w-full md:w-[180px]">
        <Select
          value={filters.status}
          onValueChange={(value) => onFiltersChange({ status: value as ProjectStatus | 'all' })}
          data-testid="status-filter"
        >
          {statusOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Select>
      </div>
      <div className="w-full md:w-[200px]">
        <Select
          value={filters.supervisorId}
          onValueChange={(value) => onFiltersChange({ supervisorId: value })}
          data-testid="supervisor-filter"
        >
          <option value="">All Supervisors</option>
          {supervisors.map((supervisor) => (
            <option key={supervisor.id} value={supervisor.id}>
              {supervisor.name}
            </option>
          ))}
        </Select>
      </div>
    </div>
  );
});