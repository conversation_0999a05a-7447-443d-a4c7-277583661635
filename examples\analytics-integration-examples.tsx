/**
 * Analytics Integration Examples
 * 
 * This file demonstrates how to integrate the real-data analytics system
 * into existing components and replace mock data with live data from hooks.
 */

import React from 'react';
import { useAnalytics } from '@/hooks/useAnalytics';
import { useDashboardData } from '@/hooks/useDashboardData';
import { useAuth } from '@/hooks/useAuth';
import { ProjectAnalytics } from '@/components/dashboard/ProjectAnalytics';
import { RealTimeAnalytics } from '@/components/analytics/RealTimeAnalytics';

/**
 * Example 1: Enhanced Dashboard with Real Analytics
 * Shows how to replace the existing dashboard analytics with real data
 */
export function EnhancedDashboardWithAnalytics() {
  const { user } = useAuth();
  
  // Get comprehensive dashboard data
  const dashboardData = useDashboardData({
    filters: {
      sortBy: 'createdAt',
      sortDirection: 'desc'
    }
  });

  // Get real-time analytics
  const analytics = useAnalytics({
    timeRange: 'quarter',
    enableRealTime: true,
    includeExtendedAnalytics: true
  });

  const { users, projects, supervisors, loading, error } = dashboardData;

  if (loading) {
    return <div>Loading dashboard analytics...</div>;
  }

  if (error) {
    return <div>Error loading analytics: {error}</div>;
  }

  return (
    <div className="space-y-8">
      <h1 className="text-3xl font-bold">Enhanced Dashboard</h1>
      
      {/* Real-time Analytics Component */}
      <RealTimeAnalytics 
        initialTimeRange="quarter"
        enableExtendedAnalytics={true}
        filters={{
          userRole: user?.role === 'manager' ? 'all' : user?.role
        }}
      />
      
      {/* Traditional Project Analytics with Real Data */}
      <div className="mt-8">
        <h2 className="text-2xl font-semibold mb-4">Project Analytics</h2>
        <ProjectAnalytics 
          projects={projects || []} 
          supervisors={supervisors || []} 
        />
      </div>

      {/* Raw Data Summary */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-medium mb-2">Data Summary</h3>
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div>
            <span className="font-medium">Projects:</span> {projects?.length || 0}
          </div>
          <div>
            <span className="font-medium">Users:</span> {users?.length || 0}
          </div>
          <div>
            <span className="font-medium">Supervisors:</span> {supervisors?.length || 0}
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Example 2: Role-Based Analytics
 * Shows different analytics views based on user role
 */
export function RoleBasedAnalytics() {
  const { user } = useAuth();
  
  // Configure analytics based on user role
  const analyticsConfig = React.useMemo(() => {
    switch (user?.role) {
      case 'manager':
        return {
          timeRange: 'year' as const,
          filters: {},
          includeExtendedAnalytics: true
        };
      case 'supervisor':
        return {
          timeRange: 'quarter' as const,
          filters: { userRole: 'supervisor' },
          includeExtendedAnalytics: true
        };
      case 'student':
        return {
          timeRange: 'month' as const,
          filters: { userRole: 'student' },
          includeExtendedAnalytics: false
        };
      default:
        return {
          timeRange: 'month' as const,
          filters: {},
          includeExtendedAnalytics: false
        };
    }
  }, [user?.role]);

  const analytics = useAnalytics(analyticsConfig);

  if (!user) {
    return <div>Please log in to view analytics</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">
          {user.role.charAt(0).toUpperCase() + user.role.slice(1)} Analytics
        </h1>
        <div className="text-sm text-muted-foreground">
          Role: {user.role} | Time Range: {analyticsConfig.timeRange}
        </div>
      </div>

      {/* Role-specific analytics display */}
      {user.role === 'manager' && (
        <ManagerAnalyticsView analytics={analytics} />
      )}
      
      {user.role === 'supervisor' && (
        <SupervisorAnalyticsView analytics={analytics} />
      )}
      
      {user.role === 'student' && (
        <StudentAnalyticsView analytics={analytics} />
      )}
    </div>
  );
}

/**
 * Example 3: Comparative Analytics
 * Shows how to compare different time periods or filters
 */
export function ComparativeAnalytics() {
  const currentPeriod = useAnalytics({
    timeRange: 'quarter',
    includeExtendedAnalytics: true
  });

  const previousPeriod = useAnalytics({
    timeRange: 'year', // Longer period for comparison
    includeExtendedAnalytics: true
  });

  if (currentPeriod.isLoading || previousPeriod.isLoading) {
    return <div>Loading comparative analytics...</div>;
  }

  const currentData = currentPeriod.analytics;
  const previousData = previousPeriod.analytics;

  if (!currentData || !previousData) {
    return <div>No data available for comparison</div>;
  }

  // Calculate percentage changes
  const projectGrowth = ((currentData.totalProjects - previousData.totalProjects) / previousData.totalProjects) * 100;
  const userGrowth = ((currentData.userEngagement.totalUsers - previousData.userEngagement.totalUsers) / previousData.userEngagement.totalUsers) * 100;

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Comparative Analytics</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Current Period */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Current Quarter</h2>
          <div className="grid grid-cols-2 gap-4">
            <MetricCard
              title="Projects"
              value={currentData.totalProjects}
              change={projectGrowth}
            />
            <MetricCard
              title="Users"
              value={currentData.userEngagement.totalUsers}
              change={userGrowth}
            />
            <MetricCard
              title="Completion Rate"
              value={`${Math.round((currentData.projectsByStatus.completed / currentData.totalProjects) * 100)}%`}
            />
            <MetricCard
              title="Avg. Completion"
              value={`${currentData.averageCompletionTime} days`}
            />
          </div>
        </div>

        {/* Previous Period */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Previous Year</h2>
          <div className="grid grid-cols-2 gap-4">
            <MetricCard
              title="Projects"
              value={previousData.totalProjects}
            />
            <MetricCard
              title="Users"
              value={previousData.userEngagement.totalUsers}
            />
            <MetricCard
              title="Completion Rate"
              value={`${Math.round((previousData.projectsByStatus.completed / previousData.totalProjects) * 100)}%`}
            />
            <MetricCard
              title="Avg. Completion"
              value={`${previousData.averageCompletionTime} days`}
            />
          </div>
        </div>
      </div>

      {/* Growth Summary */}
      <div className="p-4 bg-blue-50 rounded-lg">
        <h3 className="font-medium text-blue-900 mb-2">Growth Summary</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="text-blue-800">
            <span className="font-medium">Project Growth:</span> {projectGrowth.toFixed(1)}%
          </div>
          <div className="text-blue-800">
            <span className="font-medium">User Growth:</span> {userGrowth.toFixed(1)}%
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Example 4: Real-Time Analytics Dashboard
 * Shows how to create a live updating analytics dashboard
 */
export function LiveAnalyticsDashboard() {
  const [autoRefresh, setAutoRefresh] = React.useState(true);
  const [refreshInterval, setRefreshInterval] = React.useState(30000); // 30 seconds

  const analytics = useAnalytics({
    timeRange: 'month',
    enableRealTime: autoRefresh,
    includeExtendedAnalytics: true
  });

  // Auto-refresh functionality
  React.useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      analytics.refreshAnalytics();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, analytics.refreshAnalytics]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Live Analytics Dashboard</h1>
        
        <div className="flex items-center gap-4">
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
            />
            Auto-refresh
          </label>
          
          {autoRefresh && (
            <select
              value={refreshInterval}
              onChange={(e) => setRefreshInterval(Number(e.target.value))}
              className="text-sm border rounded px-2 py-1"
            >
              <option value={10000}>10s</option>
              <option value={30000}>30s</option>
              <option value={60000}>1m</option>
              <option value={300000}>5m</option>
            </select>
          )}
          
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <div className={`w-2 h-2 rounded-full ${analytics.isFetching ? 'bg-green-500 animate-pulse' : 'bg-gray-300'}`} />
            {analytics.isFetching ? 'Updating...' : 'Live'}
          </div>
        </div>
      </div>

      <RealTimeAnalytics 
        initialTimeRange="month"
        enableExtendedAnalytics={true}
      />

      {analytics.lastUpdated && (
        <div className="text-xs text-muted-foreground text-center">
          Last updated: {analytics.lastUpdated.toLocaleString()}
        </div>
      )}
    </div>
  );
}

// Helper Components for Role-Based Views
function ManagerAnalyticsView({ analytics }: { analytics: any }) {
  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold">Manager Overview</h2>
      <RealTimeAnalytics 
        initialTimeRange="year"
        enableExtendedAnalytics={true}
      />
    </div>
  );
}

function SupervisorAnalyticsView({ analytics }: { analytics: any }) {
  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold">Supervisor Dashboard</h2>
      <RealTimeAnalytics 
        initialTimeRange="quarter"
        enableExtendedAnalytics={true}
        filters={{ userRole: 'supervisor' }}
      />
    </div>
  );
}

function StudentAnalyticsView({ analytics }: { analytics: any }) {
  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold">Student Progress</h2>
      <RealTimeAnalytics 
        initialTimeRange="month"
        enableExtendedAnalytics={false}
        filters={{ userRole: 'student' }}
      />
    </div>
  );
}

function MetricCard({ 
  title, 
  value, 
  change 
}: { 
  title: string; 
  value: string | number; 
  change?: number;
}) {
  return (
    <div className="p-4 border rounded-lg">
      <div className="text-sm text-muted-foreground">{title}</div>
      <div className="text-2xl font-bold">{value}</div>
      {change !== undefined && (
        <div className={`text-xs ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
          {change >= 0 ? '+' : ''}{change.toFixed(1)}%
        </div>
      )}
    </div>
  );
}
