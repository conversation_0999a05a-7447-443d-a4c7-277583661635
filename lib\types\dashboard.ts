import { User, Project, ProjectStatus, Document } from '@/lib/types';

export interface DashboardData {
  users: User[];
  projects: Project[];
  supervisors: User[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export interface ProjectAnalyticsData {
  totalProjects: number;
  projectsByStatus: {
    active: number;
    completed: number;
    archived: number;
    suspended: number;
    pending_supervisor: number;
  };
  projectsByMonth: EnhancedMonthlyData[];
  supervisorLoad: {
    supervisorId: string;
    supervisorName: string;
    projectCount: number;
  }[];
  averageCompletionTime: number; // in days
  projectTrends: {
    period: string;
    newProjects: number;
    completedProjects: number;
  }[];
}

export interface EnhancedMonthlyData {
  month: string;
  shortMonth: string;
  count: number;
  completed: number;
  active: number;
  growthRate: number;
  completionRate: number;
  statusBreakdown: {
    active: number;
    completed: number;
    archived: number;
    suspended: number;
    pending_supervisor: number;
  };
  monthStart: Date;
  monthEnd: Date;
  projects: {
    id: string;
    title: string;
    status: string;
    createdAt: string;
  }[];
}

export interface ProjectStatistics {
  documentsCount: number;
  commentsCount: number;
  lastActivity: Date;
  progressPercentage: number;
  milestoneCompletion: {
    total: number;
    completed: number;
    percentage: number;
  };
  teamMembers: {
    students: number;
    supervisors: number;
    total: number;
  };
}

export interface ProjectListProps {
  projects: Project[];
  users: User[];
  supervisors: User[];
  sortBy: 'createdAt' | 'title' | 'lastActivity';
  filterStatus: ProjectStatus | 'all';
  onSortChange: (sortBy: 'createdAt' | 'title' | 'lastActivity') => void;
  onFilterChange: (status: ProjectStatus | 'all') => void;
}

export interface ProjectDetailsProps {
  project: Project;
  documents: Document[];
  users: User[];
}



