# ==============================================
# Docker Environment Configuration
# ==============================================
# Copy this file to .env and fill in your values

# ==============================================
# Application Configuration
# ==============================================
NODE_ENV=production
DOMAIN=localhost
SSL_EMAIL=<EMAIL>

# ==============================================
# Appwrite Configuration
# ==============================================
APPWRITE_PROJECT_ID=your-project-id
APPWRITE_DATABASE_ID=your-database-id

# ==============================================
# Appwrite System Configuration
# ==============================================
_APP_ENV=production
_APP_LOCALE=en
_APP_CONSOLE_WHITELIST_ROOT=enabled
_APP_CONSOLE_WHITELIST_EMAILS=
_APP_CONSOLE_WHITELIST_IPS=
_APP_SYSTEM_EMAIL_NAME=Thesis Management System
_APP_SYSTEM_EMAIL_ADDRESS=<EMAIL>
_APP_SYSTEM_SECURITY_EMAIL_ADDRESS=<EMAIL>
_APP_DOMAIN=http://localhost
_APP_DOMAIN_TARGET=http://localhost

# ==============================================
# Security Keys (GENERATE SECURE RANDOM STRINGS)
# ==============================================
# Generate with: openssl rand -base64 32
_APP_OPENSSL_KEY_V1=your-openssl-key-here
_APP_EXECUTOR_SECRET=your-executor-secret-here

# ==============================================
# Database Configuration
# ==============================================
_APP_DB_USER=appwrite
_APP_DB_PASS=your-secure-db-password
_APP_DB_ROOT_PASS=your-secure-root-password

# ==============================================
# Email Configuration (Optional)
# ==============================================
_APP_SMTP_HOST=smtp.gmail.com
_APP_SMTP_PORT=587
_APP_SMTP_SECURE=tls
_APP_SMTP_USERNAME=<EMAIL>
_APP_SMTP_PASSWORD=your-app-password

# ==============================================
# Storage Configuration
# ==============================================
# Local storage limits (in bytes)
_APP_STORAGE_LIMIT=30000000
_APP_STORAGE_PREVIEW_LIMIT=20000000
_APP_STORAGE_ANTIVIRUS=disabled
_APP_STORAGE_DEVICE=local

# ==============================================
# AWS S3 Configuration (Optional)
# ==============================================
# _APP_STORAGE_DEVICE=s3
# _APP_STORAGE_S3_ACCESS_KEY=your-s3-access-key
# _APP_STORAGE_S3_SECRET=your-s3-secret-key
# _APP_STORAGE_S3_REGION=us-east-1
# _APP_STORAGE_S3_BUCKET=your-bucket-name

# ==============================================
# Functions Configuration
# ==============================================
_APP_FUNCTIONS_SIZE_LIMIT=30000000
_APP_FUNCTIONS_TIMEOUT=900
_APP_FUNCTIONS_BUILD_TIMEOUT=900
_APP_FUNCTIONS_CONTAINERS=10
_APP_FUNCTIONS_CPUS=0
_APP_FUNCTIONS_MEMORY=0
_APP_FUNCTIONS_MEMORY_SWAP=0
_APP_FUNCTIONS_RUNTIMES=node-16.0,php-8.0,python-3.9,ruby-3.0

# ==============================================
# Maintenance Configuration
# ==============================================
_APP_MAINTENANCE_INTERVAL=86400
_APP_MAINTENANCE_RETENTION_EXECUTION=1209600
_APP_MAINTENANCE_RETENTION_CACHE=2592000
_APP_MAINTENANCE_RETENTION_ABUSE=86400
_APP_MAINTENANCE_RETENTION_AUDIT=1209600

# ==============================================
# Usage Stats Configuration
# ==============================================
_APP_USAGE_STATS=disabled
