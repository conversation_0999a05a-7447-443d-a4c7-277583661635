import { Project, ProjectStatus, User } from "@/lib/types";
import { <PERSON><PERSON> } from "@/components/ui/button";

type SortField = "title" | "lastActivity" | "createdAt";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ProjectCard } from "@/components/projects/ProjectCard";
import { useRouter } from "next/navigation";
import { PlusCircle, Filter, SortAsc } from "lucide-react";
import { motion } from "framer-motion";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";

interface ProjectListProps {
  projects: Project[];
  users: User[];
  supervisors: User[];
  sortBy: SortField;
  filterStatus: string;
  onSortChange: (value: SortField) => void;
  onFilterChange: (value: ProjectStatus | "all") => void;
  isLoading: boolean;
}

export function ProjectList({
  projects,
  users,
  supervisors,
  sortBy,
  filterStatus,
  onSortChange,
  onFilterChange,
  isLoading,
}: ProjectListProps) {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Sort projects based on sortBy
  const sortedProjects = [...projects].sort((a, b) => {
    switch (sortBy) {
      case "title":
        return a.title.localeCompare(b.title);
      case "lastActivity":
        return (
          new Date(b.lastActivity).getTime() -
          new Date(a.lastActivity).getTime()
        );
      case "createdAt":
      default:
        return (
          new Date(b.createdAt || 0).getTime() -
          new Date(a.createdAt || 0).getTime()
        );
    }
  });

  // Filter projects based on status and search term
  const filteredProjects = sortedProjects
    .filter((project) =>
      filterStatus === "all" ? true : project.status === filterStatus
    )
    .filter(
      (project) =>
        searchTerm === "" ||
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase())
    );

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="relative flex-1">
          <Input
            placeholder="Search projects..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
          <Filter className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        </div>

        <div className="flex gap-2">
          <Tabs
            value={viewMode}
            onValueChange={(v) => setViewMode(v as "grid" | "list")}
            className="hidden sm:block"
          >
            <TabsList className="h-9">
              <TabsTrigger value="grid" className="px-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <rect width="7" height="7" x="3" y="3" rx="1" />
                  <rect width="7" height="7" x="14" y="3" rx="1" />
                  <rect width="7" height="7" x="14" y="14" rx="1" />
                  <rect width="7" height="7" x="3" y="14" rx="1" />
                </svg>
              </TabsTrigger>
              <TabsTrigger value="list" className="px-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <line x1="3" x2="21" y1="6" y2="6" />
                  <line x1="3" x2="21" y1="12" y2="12" />
                  <line x1="3" x2="21" y1="18" y2="18" />
                </svg>
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Select value={sortBy} onValueChange={onSortChange}>
            <SelectTrigger className="w-[160px]">
              <div className="flex items-center gap-2">
                <SortAsc className="h-4 w-4" />
                <SelectValue placeholder="Sort by" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="createdAt">Date Created</SelectItem>
              <SelectItem value="title">Title</SelectItem>
              <SelectItem value="lastActivity">Last Activity</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex flex-wrap gap-2">
        <Button
          variant={filterStatus === "all" ? "default" : "outline"}
          size="sm"
          onClick={() => onFilterChange("all")}
        >
          All
        </Button>
        <Button
          variant={filterStatus === "active" ? "default" : "outline"}
          size="sm"
          onClick={() => onFilterChange("active")}
          className="bg-green-500/10 text-green-700 border-green-200 hover:bg-green-500/20 hover:text-green-800"
        >
          Active
        </Button>
        <Button
          variant={filterStatus === "completed" ? "default" : "outline"}
          size="sm"
          onClick={() => onFilterChange("completed")}
          className="bg-blue-500/10 text-blue-700 border-blue-200 hover:bg-blue-500/20 hover:text-blue-800"
        >
          Completed
        </Button>
        <Button
          variant={filterStatus === "archived" ? "default" : "outline"}
          size="sm"
          onClick={() => onFilterChange("archived")}
          className="bg-gray-500/10 text-gray-700 border-gray-200 hover:bg-gray-500/20 hover:text-gray-800"
        >
          Archived
        </Button>
        <Button
          variant={
            filterStatus === "pending_supervisor" ? "default" : "outline"
          }
          size="sm"
          onClick={() => onFilterChange("pending_supervisor")}
          className="bg-amber-500/10 text-amber-700 border-amber-200 hover:bg-amber-500/20 hover:text-amber-800"
        >
          Pending
        </Button>
      </div>

      {viewMode === "grid" ? (
        isLoading ? (
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {Array(6)
              .fill(0)
              .map((_, i) => (
                <div key={i} className="border rounded-lg p-4">
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2 mb-4" />
                  <div className="flex justify-between">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <Skeleton className="h-6 w-20" />
                  </div>
                </div>
              ))}
          </div>
        ) : (
          <motion.div
            className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            {filteredProjects.map((project) => {
              const student = users.find((u) => u.id === project.studentId);

              return (
                <motion.div
                  key={project.id}
                  className="group"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <ProjectCard
                    project={project}
                    student={student}
                    onClick={() => router.push(`/projects/${project.id}`)}
                  />
                </motion.div>
              );
            })}
          </motion.div>
        )
      ) : isLoading ? (
        <div className="space-y-3">
          {Array(6)
            .fill(0)
            .map((_, i) => (
              <div key={i} className="border rounded-lg p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2 mb-4" />
                <div className="flex justify-between">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <Skeleton className="h-6 w-20" />
                </div>
              </div>
            ))}
        </div>
      ) : (
        <motion.div
          className="space-y-3"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          {filteredProjects.map((project) => {
            const student = users.find((u) => u.id === project.studentId);
            const progress =
              project.documents?.length > 0
                ? Math.round(
                    (project.documents.filter((d) =>
                      typeof d === "object" ? d.status === "approved" : false
                    ).length /
                      project.documents.length) *
                      100
                  )
                : 0;

            return (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
                className="border rounded-lg p-4 hover:bg-accent/50 cursor-pointer transition-colors"
                onClick={() => router.push(`/projects/${project.id}`)}
                style={{
                  borderLeftWidth: "4px",
                  borderLeftColor: getStatusColor(project.status),
                }}
              >
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium">{project.title}</h3>
                      <Badge className={getStatusClass(project.status)}>
                        {project.status
                          .split("_")
                          .map(
                            (word) =>
                              word.charAt(0).toUpperCase() + word.slice(1)
                          )
                          .join(" ")}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground line-clamp-1 mt-1">
                      {project.description}
                    </p>
                  </div>

                  <div className="flex items-center gap-4 text-sm">
                    {student && (
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={student.profileImage} />
                          <AvatarFallback>
                            {student.name?.charAt(0) || "?"}
                          </AvatarFallback>
                        </Avatar>
                        <span className="hidden md:inline">{student.name}</span>
                      </div>
                    )}

                    <div className="flex items-center gap-2">
                      <div className="w-24 h-2 bg-gray-100 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-green-500 rounded-full"
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                      <span className="text-xs">{progress}%</span>
                    </div>

                    <span className="text-xs text-muted-foreground">
                      {format(new Date(project.lastActivity), "MMM d, yyyy")}
                    </span>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </motion.div>
      )}

      {filteredProjects.length === 0 && (
        <div className="text-center py-12 border rounded-lg bg-muted/20">
          <div className="mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="40"
              height="40"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mx-auto text-muted-foreground"
            >
              <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
              <polyline points="14 2 14 8 20 8" />
              <path d="M12 18v-6" />
              <path d="M8 18v-1" />
              <path d="M16 18v-3" />
            </svg>
          </div>
          <p className="text-muted-foreground mb-4">No projects found</p>
          {(searchTerm || filterStatus !== "all") && (
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm("");
                onFilterChange("all");
              }}
            >
              Clear filters
            </Button>
          )}
        </div>
      )}
    </div>
  );
}

function getStatusClass(status: string): string {
  switch (status) {
    case "active":
      return "bg-green-500 hover:bg-green-600";
    case "completed":
      return "bg-blue-500 hover:bg-blue-600";
    case "archived":
      return "bg-gray-500 hover:bg-gray-600";
    case "suspended":
      return "bg-red-500 hover:bg-red-600";
    case "pending_supervisor":
      return "bg-amber-500 hover:bg-amber-600";
    default:
      return "bg-gray-500 hover:bg-gray-600";
  }
}

function calculateProgress(project: Project): number {
  if (!project.documents?.length) return 0;
  return Math.round(
    (project.documents.filter((d) =>
      typeof d === "object" ? d.status === "approved" : false
    ).length /
      project.documents.length) *
      100
  );
}

function formatDate(dateString: string): string {
  return format(new Date(dateString), "MMM d");
}

function getStatusColor(status: string): string {
  switch (status) {
    case "active":
      return "#10b981"; // green-500
    case "completed":
      return "#3b82f6"; // blue-500
    case "archived":
      return "#6b7280"; // gray-500
    case "suspended":
      return "#ef4444"; // red-500
    case "pending_supervisor":
      return "#f59e0b"; // amber-500
    default:
      return "#6b7280"; // gray-500
  }
}
