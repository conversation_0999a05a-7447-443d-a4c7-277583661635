"use client";

import { Project } from "@/lib/types";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { memo } from "react";

interface RecentActivityCardProps {
  projects: Project[];
  isLoading?: boolean;
}

export const RecentActivityCard = memo(function RecentActivityCard({ 
  projects, 
  isLoading = false 
}: RecentActivityCardProps) {
  const router = useRouter();

  if (isLoading) {
    return (
      <div className="dashboard-card">
        <div className="dashboard-card-header">
          <div className="flex items-center justify-between">
            <div>
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-48 mt-1" />
            </div>
            <Skeleton className="h-9 w-20" />
          </div>
        </div>
        <div className="dashboard-card-content">
          <div className="space-y-4">
            {Array(3).fill(0).map((_, i) => (
              <div key={i} className="dashboard-list-item">
                <div className="flex-1 min-w-0">
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-4 w-1/2 mt-1" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-card">
      <div className="dashboard-card-header">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="dashboard-section-title">Recent Activity</h3>
            <p className="dashboard-subtitle">Latest updates and changes</p>
          </div>
          <button
            className="dashboard-button-outline"
            onClick={() => router.push("/projects?sort=lastActivity")}
          >
            View All
          </button>
        </div>
      </div>
      <div className="dashboard-card-content">
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-3">
            {projects.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No recent activity found
              </div>
            ) : (
              projects.map((project) => (
                <div
                  key={project.id}
                  className="dashboard-list-item"
                  onClick={() => router.push(`/projects/${project.id}`)}
                >
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate text-card-foreground">{project.title}</p>
                    <p className="text-sm text-muted-foreground">
                      Updated {format(new Date(project.lastActivity), "PPP")}
                    </p>
                  </div>
                  <span className={`status-indicator ${
                    project.status === "active" ? "status-active" :
                    project.status === "completed" ? "status-completed" :
                    "status-pending"
                  }`}>
                    {project.status}
                  </span>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
});
