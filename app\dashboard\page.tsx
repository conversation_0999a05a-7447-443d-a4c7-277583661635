"use client";

import { <PERSON><PERSON> } from "@/components/layout/Header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useState, useMemo, useCallback, useEffect } from "react";
import { Project, ProjectStatus, User, UserRole, Document } from "@/lib/types";
import { useAuth } from "@/hooks/useAuth";
import { useDashboardData } from "@/hooks/useDashboardData";
import { useAnalytics } from "@/hooks/useAnalytics";
import { ProjectList } from "@/components/dashboard/ProjectList";
import { ProjectAnalytics } from "@/components/dashboard/ProjectAnalytics";
import { DashboardWidgets } from "@/components/dashboard/DashboardWidgets";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Plus } from "lucide-react";
import { useNotifications } from "@/hooks/useNotifications";
import { RecentActivityCard } from "@/components/dashboard/RecentActivityCard";
import { UpcomingDeadlinesCard } from "@/components/dashboard/UpcomingDeadlinesCard";
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { useRouter } from "next/navigation";
import { EnhancedProjectsByMonth } from "@/components/dashboard/EnhancedProjectsByMonth";
import { RealTimeAnalytics } from "@/components/analytics/RealTimeAnalytics";
import { generateAnalyticsData } from "@/lib/utils/analytics";
import { StudentDashboardContent } from "@/components/dashboard/role-content/StudentDashboardContent";
import { SupervisorDashboardContent } from "@/components/dashboard/role-content/SupervisorDashboardContent";
import { ManagerDashboardContent } from "@/components/dashboard/role-content/ManagerDashboardContent";
import { AdminDashboardContent } from "@/components/dashboard/role-content/AdminDashboardContent";

export default function DashboardPage() {
  // Auth and data hooks
  const { user: currentUser } = useAuth();
  const router = useRouter();
  const { usersQuery, projectsQuery, refetch: refreshDashboardData } = useDashboardData();
  const userId = currentUser?.id || '';
  const {
    notifications,
    isLoading: isLoadingNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    deleteAllNotifications
  } = useNotifications(userId);

  // Enhanced analytics with real-time data
  const {
    analytics: comprehensiveAnalytics,
    isLoading: isLoadingAnalytics,
    error: analyticsError,
    refreshAnalytics,
    timeRange: analyticsTimeRange,
    setTimeRange: setAnalyticsTimeRange
  } = useAnalytics({
    timeRange: 'quarter',
    enableRealTime: true,
    includeExtendedAnalytics: true,
    filters: {
      userRole: currentUser?.role === 'manager' || currentUser?.role === 'admin' ? 'all' : currentUser?.role
    }
  });

  // Extract data with default values and proper typing
  const { data: users = [] as User[], isLoading: usersLoading, error: usersError } = usersQuery;
  const { data: projects = [] as Project[], isLoading: projectsLoading, error: projectsError } = projectsQuery;

  // UI state
  const [sortBy, setSortBy] = useState<"createdAt" | "title" | "lastActivity">("lastActivity");
  const [filterStatus, setFilterStatus] = useState<ProjectStatus | "all">("all");
  const [activeTab, setActiveTab] = useState<"overview" | "projects" | "analytics" | "role-specific">("overview");

  // Redirect legacy role-specific dashboard URLs to the unified dashboard
  useEffect(() => {
    // Check if we're on a role-specific dashboard URL
    const pathname = window.location.pathname;
    if (pathname.match(/^\/dashboard\/(student|supervisor|manager|admin)$/)) {
      router.replace('/dashboard');
    }
  }, [router]);

  // Memoized derived data
  const supervisors = useMemo(() =>
    users.filter((user: User) => user.role === "supervisor"),
    [users]
  );

  const recentProjects = useMemo(() =>
    [...(projects as Project[])]
      .sort((a: Project, b: Project) => new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime())
      .slice(0, 5),
    [projects]
  );

  const upcomingDeadlines = useMemo(() =>
    (projects as Project[])
      .filter((p: Project) => p.status === "active")
      .sort((a: Project, b: Project) => new Date(a.deadline).getTime() - new Date(b.deadline).getTime())
      .slice(0, 5),
    [projects]
  );

  // Generate analytics data for enhanced charts
  const analyticsData = useMemo(() =>
    generateAnalyticsData(projects as Project[], supervisors, 'quarter'),
    [projects, supervisors]
  );

  // Event handlers
  const handleSortChange = useCallback((value: string) =>
    setSortBy(value as "createdAt" | "title" | "lastActivity"),
    []
  );

  const handleFilterChange = useCallback((value: string) =>
    setFilterStatus(value as ProjectStatus | "all"),
    []
  );



  // Status indicators
  const isLoading = usersLoading || projectsLoading;
  const hasError = usersError || projectsError;
  const errorMessage = usersError
    ? String(usersError)
    : projectsError
      ? String(projectsError)
      : "An error occurred while loading the dashboard";

  // Get role-specific data
  const getRoleSpecificData = () => {
    if (!currentUser) return null;

    // Student-specific data
    if (currentUser.role === 'student') {
      const studentProjects = (projects as Project[]).filter((p: Project) => p.studentId === currentUser.id);
      const studentDocuments = studentProjects.flatMap((p: Project) => (p.documents || []) as Document[]);

      return {
        projects: studentProjects,
        documents: studentDocuments,
        supervisors: users.filter((u: User) =>
          studentProjects.some((p: Project) => p.supervisorIds?.includes(u.id))
        )
      };
    }

    // Supervisor-specific data
    if (currentUser.role === 'supervisor') {
      const supervisorProjects = (projects as Project[]).filter((p: Project) =>
        p.supervisorIds?.includes(currentUser.id)
      );

      const supervisedStudents = users.filter((u: User) =>
        u.role === 'student' &&
        supervisorProjects.some((p: Project) => p.studentId === u.id)
      );

      const supervisorDocuments = supervisorProjects.flatMap((p: Project) => (p.documents || []) as Document[]);

      return {
        projects: supervisorProjects,
        documents: supervisorDocuments,
        students: supervisedStudents
      };
    }

    // For manager and admin, return all data
    return {
      projects: projects as Project[],
      documents: (projects as Project[]).flatMap((p: Project) => (p.documents || []) as Document[]),
      users
    };
  };



  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="container mx-auto px-6 py-8">
        {/* Dashboard Header with user info and notifications */}
        <DashboardHeader
          currentUser={currentUser}
          notifications={notifications}
          isLoadingNotifications={isLoadingNotifications}
          onMarkAsRead={markAsRead}
          onMarkAllAsRead={markAllAsRead}
          onDelete={deleteNotification}
          onDeleteAll={deleteAllNotifications}
        />

        {/* Error handling */}
        {hasError ? (
          <div className="p-6 max-w-md mx-auto">
            <Alert variant="destructive">
              <AlertTitle>Error Loading Dashboard</AlertTitle>
              <AlertDescription>{errorMessage}</AlertDescription>
            </Alert>
            <Button
              onClick={refreshDashboardData}
              className="mt-4 w-full"
              disabled={isLoading}
            >
              {isLoading ? 'Retrying...' : 'Retry'}
            </Button>
          </div>
        ) : (
          <Tabs
            defaultValue="overview"
            className="space-y-6"
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as "overview" | "projects" | "analytics" | "role-specific")}
          >
            <TabsList className="bg-muted/50 p-1">
              <TabsTrigger value="overview" className="text-sm">Overview</TabsTrigger>
              <TabsTrigger value="projects" className="text-sm">Projects</TabsTrigger>
              <TabsTrigger value="analytics" className="text-sm">Analytics</TabsTrigger>
              <TabsTrigger value="role-specific" className="text-sm">
                {currentUser?.role === 'student' ? 'My Dashboard' :
                 currentUser?.role === 'supervisor' ? 'Supervision' :
                 currentUser?.role === 'manager' ? 'Management' :
                 currentUser?.role === 'admin' ? 'Admin' : 'Role Dashboard'}
              </TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-8">
              <DashboardWidgets projects={projects as Project[]} users={users as User[]} />

              {/* Enhanced Projects by Month */}
              {analyticsData && (
                <EnhancedProjectsByMonth
                  data={analyticsData.projectsByMonth}
                />
              )}

              <div className="dashboard-grid-2">
                <RecentActivityCard
                  projects={recentProjects}
                  isLoading={isLoading}
                />
                <UpcomingDeadlinesCard
                  projects={upcomingDeadlines}
                  isLoading={isLoading}
                />
              </div>
            </TabsContent>

            {/* Projects Tab */}
            <TabsContent value="projects">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <h2 className="text-2xl font-semibold tracking-tight">Projects</h2>
                    <p className="text-sm text-muted-foreground">
                      Manage and track all your projects
                    </p>
                  </div>
                  <Button onClick={() => router.push('/projects/new')}>
                    <Plus className="mr-2 h-4 w-4" /> New Project
                  </Button>
                </div>
                <ProjectList
                  isLoading={isLoading}
                  projects={projects as Project[]}
                  users={users as User[]}
                  supervisors={supervisors}
                  sortBy={sortBy}
                  filterStatus={filterStatus}
                  onSortChange={handleSortChange}
                  onFilterChange={handleFilterChange}
                />
              </div>
            </TabsContent>

            {/* Analytics Tab */}
            <TabsContent value="analytics">
              <div className="space-y-4">
                <div className="space-y-1">
                  <h2 className="text-2xl font-semibold tracking-tight">Analytics</h2>
                  <p className="text-sm text-muted-foreground">
                    Real-time project statistics and comprehensive insights
                  </p>
                </div>

                {/* Real-time Analytics Component */}
                <RealTimeAnalytics
                  initialTimeRange={analyticsTimeRange}
                  enableExtendedAnalytics={true}
                  filters={{
                    userRole: currentUser?.role === 'manager' || currentUser?.role === 'admin' ? 'all' : currentUser?.role
                  }}
                />

                {/* Traditional Analytics for comparison */}
                <div className="mt-8">
                  <h3 className="text-lg font-semibold mb-4">Traditional Project Analytics</h3>
                  <ProjectAnalytics projects={projects as Project[]} supervisors={supervisors} />
                </div>
              </div>
            </TabsContent>

            {/* Role-specific Tab */}
            <TabsContent value="role-specific">
              {currentUser?.role === 'student' && (
                <StudentDashboardContent
                  projects={getRoleSpecificData()?.projects || []}
                  documents={getRoleSpecificData()?.documents || []}
                  supervisors={getRoleSpecificData()?.supervisors || []}
                  isLoading={isLoading}
                />
              )}

              {currentUser?.role === 'supervisor' && (
                <SupervisorDashboardContent
                  projects={getRoleSpecificData()?.projects || []}
                  documents={getRoleSpecificData()?.documents || []}
                  students={getRoleSpecificData()?.students || []}
                  isLoading={isLoading}
                />
              )}

              {currentUser?.role === 'manager' && (
                <ManagerDashboardContent
                  users={users}
                  projects={projects as Project[]}
                  isLoading={isLoading}
                />
              )}

              {currentUser?.role === 'admin' && (
                <AdminDashboardContent
                  users={users}
                  projects={projects as Project[]}
                  isLoading={isLoading}
                />
              )}
            </TabsContent>
          </Tabs>
        )}
      </main>
    </div>
  );
}
