import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Document, User } from '@/lib/types';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';

interface DocumentsTabProps {
  documents: Document[];
  students: User[];
}

export const DocumentsTab = ({ documents, students }: DocumentsTabProps) => {
  const router = useRouter();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Documents Pending Review</CardTitle>
        <CardDescription>
          Review and provide feedback on student submissions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {documents
            .filter((doc) => doc.status === 'under_review')
            .map((doc) => {
              const student = students.find(s => s.id === doc.studentId);
              return (
                <div
                  key={doc.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <Avatar>
                      <AvatarImage src={student?.profileImage} alt={student?.name} />
                      <AvatarFallback>{student?.name?.slice(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{doc.title}</p>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{student?.name}</span>
                        <span>•</span>
                        <span>Submitted {format(doc.updatedAt, 'MMM d')}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="capitalize">
                      {doc.type}
                    </Badge>
                    <Button 
                      onClick={() => router.push(`/documents/${doc.id}`)}
                    >
                      Review
                    </Button>
                  </div>
                </div>
              );
            })}
        </div>
      </CardContent>
    </Card>
  );
};